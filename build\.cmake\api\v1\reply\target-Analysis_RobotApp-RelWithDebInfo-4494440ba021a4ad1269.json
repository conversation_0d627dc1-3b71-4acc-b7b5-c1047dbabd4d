{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/RelWithDebInfo/Analysis_RobotApp.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/RelWithDebInfo/Analysis_RobotApp.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "include_directories"], "files": ["builder/cmake/executable.cmake", "Analysis_Robot/App/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "Analysis_Robot/drivers/restInterfaceDriver/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "Analysis_Robot/drivers/robotDriver/CMakeLists.txt", "Analysis_Robot/drivers/moistureAnalyzerDriver/CMakeLists.txt", "Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt", "Analysis_Robot/drivers/plcDriver/CMakeLists.txt", "Analysis_Robot/drivers/balanceDriver/CMakeLists.txt", "Analysis_Robot/drivers/aixsDriver/CMakeLists.txt", "Analysis_Robot/algorithms/pouringControl/CMakeLists.txt", "Analysis_Robot/algorithms/coordinateTransform/CMakeLists.txt", "fuxicommon/CMakeLists.txt", "Analysis_Robot/algorithms/tcpPositionMaintain/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "builder/cmake/add_libmodbus.cmake", "builder/cmake/add_rttr.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"file": 4}, {"command": 1, "file": 4, "line": 2, "parent": 6}, {"file": 3, "parent": 7}, {"command": 3, "file": 3, "line": 90, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 9}, {"command": 1, "file": 3, "line": 1, "parent": 8}, {"file": 2, "parent": 11}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 8, "parent": 13}, {"command": 5, "file": 8, "line": 33, "parent": 14}, {"file": 7, "parent": 15}, {"command": 5, "file": 7, "line": 610, "parent": 16}, {"file": 6, "parent": 17}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 19}, {"file": 5, "parent": 20}, {"command": 4, "file": 5, "line": 103, "parent": 21}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 23}, {"file": 9, "parent": 24}, {"command": 4, "file": 9, "line": 103, "parent": 25}, {"file": 10}, {"command": 1, "file": 10, "line": 2, "parent": 27}, {"file": 3, "parent": 28}, {"command": 3, "file": 3, "line": 90, "parent": 29}, {"command": 2, "file": 3, "line": 53, "parent": 30}, {"command": 1, "file": 3, "line": 1, "parent": 29}, {"file": 2, "parent": 32}, {"command": 1, "file": 2, "line": 81, "parent": 33}, {"file": 8, "parent": 34}, {"command": 5, "file": 8, "line": 33, "parent": 35}, {"file": 7, "parent": 36}, {"command": 5, "file": 7, "line": 610, "parent": 37}, {"file": 6, "parent": 38}, {"command": 6, "file": 6, "line": 262, "parent": 39}, {"command": 5, "file": 6, "line": 141, "parent": 40}, {"file": 5, "parent": 41}, {"command": 4, "file": 5, "line": 103, "parent": 42}, {"command": 6, "file": 6, "line": 262, "parent": 39}, {"command": 5, "file": 6, "line": 141, "parent": 44}, {"file": 9, "parent": 45}, {"command": 4, "file": 9, "line": 103, "parent": 46}, {"file": 11}, {"command": 1, "file": 11, "line": 2, "parent": 48}, {"file": 3, "parent": 49}, {"command": 3, "file": 3, "line": 90, "parent": 50}, {"command": 2, "file": 3, "line": 53, "parent": 51}, {"command": 1, "file": 3, "line": 1, "parent": 50}, {"file": 2, "parent": 53}, {"command": 1, "file": 2, "line": 81, "parent": 54}, {"file": 8, "parent": 55}, {"command": 5, "file": 8, "line": 33, "parent": 56}, {"file": 7, "parent": 57}, {"command": 5, "file": 7, "line": 610, "parent": 58}, {"file": 6, "parent": 59}, {"command": 6, "file": 6, "line": 262, "parent": 60}, {"command": 5, "file": 6, "line": 141, "parent": 61}, {"file": 5, "parent": 62}, {"command": 4, "file": 5, "line": 103, "parent": 63}, {"command": 6, "file": 6, "line": 262, "parent": 60}, {"command": 5, "file": 6, "line": 141, "parent": 65}, {"file": 9, "parent": 66}, {"command": 4, "file": 9, "line": 103, "parent": 67}, {"file": 12}, {"command": 1, "file": 12, "line": 2, "parent": 69}, {"file": 3, "parent": 70}, {"command": 3, "file": 3, "line": 90, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 72}, {"command": 1, "file": 3, "line": 1, "parent": 71}, {"file": 2, "parent": 74}, {"command": 1, "file": 2, "line": 81, "parent": 75}, {"file": 8, "parent": 76}, {"command": 5, "file": 8, "line": 33, "parent": 77}, {"file": 7, "parent": 78}, {"command": 5, "file": 7, "line": 610, "parent": 79}, {"file": 6, "parent": 80}, {"command": 6, "file": 6, "line": 262, "parent": 81}, {"command": 5, "file": 6, "line": 141, "parent": 82}, {"file": 5, "parent": 83}, {"command": 4, "file": 5, "line": 103, "parent": 84}, {"command": 6, "file": 6, "line": 262, "parent": 81}, {"command": 5, "file": 6, "line": 141, "parent": 86}, {"file": 9, "parent": 87}, {"command": 4, "file": 9, "line": 103, "parent": 88}, {"file": 13}, {"command": 1, "file": 13, "line": 2, "parent": 90}, {"file": 3, "parent": 91}, {"command": 3, "file": 3, "line": 90, "parent": 92}, {"command": 2, "file": 3, "line": 53, "parent": 93}, {"command": 1, "file": 3, "line": 1, "parent": 92}, {"file": 2, "parent": 95}, {"command": 1, "file": 2, "line": 81, "parent": 96}, {"file": 8, "parent": 97}, {"command": 5, "file": 8, "line": 33, "parent": 98}, {"file": 7, "parent": 99}, {"command": 5, "file": 7, "line": 610, "parent": 100}, {"file": 6, "parent": 101}, {"command": 6, "file": 6, "line": 262, "parent": 102}, {"command": 5, "file": 6, "line": 141, "parent": 103}, {"file": 5, "parent": 104}, {"command": 4, "file": 5, "line": 103, "parent": 105}, {"command": 6, "file": 6, "line": 262, "parent": 102}, {"command": 5, "file": 6, "line": 141, "parent": 107}, {"file": 9, "parent": 108}, {"command": 4, "file": 9, "line": 103, "parent": 109}, {"file": 14}, {"command": 1, "file": 14, "line": 2, "parent": 111}, {"file": 3, "parent": 112}, {"command": 3, "file": 3, "line": 90, "parent": 113}, {"command": 2, "file": 3, "line": 53, "parent": 114}, {"command": 1, "file": 3, "line": 1, "parent": 113}, {"file": 2, "parent": 116}, {"command": 1, "file": 2, "line": 81, "parent": 117}, {"file": 8, "parent": 118}, {"command": 5, "file": 8, "line": 33, "parent": 119}, {"file": 7, "parent": 120}, {"command": 5, "file": 7, "line": 610, "parent": 121}, {"file": 6, "parent": 122}, {"command": 6, "file": 6, "line": 262, "parent": 123}, {"command": 5, "file": 6, "line": 141, "parent": 124}, {"file": 5, "parent": 125}, {"command": 4, "file": 5, "line": 103, "parent": 126}, {"command": 6, "file": 6, "line": 262, "parent": 123}, {"command": 5, "file": 6, "line": 141, "parent": 128}, {"file": 9, "parent": 129}, {"command": 4, "file": 9, "line": 103, "parent": 130}, {"file": 15}, {"command": 1, "file": 15, "line": 2, "parent": 132}, {"file": 3, "parent": 133}, {"command": 3, "file": 3, "line": 90, "parent": 134}, {"command": 2, "file": 3, "line": 53, "parent": 135}, {"command": 1, "file": 3, "line": 1, "parent": 134}, {"file": 2, "parent": 137}, {"command": 1, "file": 2, "line": 81, "parent": 138}, {"file": 8, "parent": 139}, {"command": 5, "file": 8, "line": 33, "parent": 140}, {"file": 7, "parent": 141}, {"command": 5, "file": 7, "line": 610, "parent": 142}, {"file": 6, "parent": 143}, {"command": 6, "file": 6, "line": 262, "parent": 144}, {"command": 5, "file": 6, "line": 141, "parent": 145}, {"file": 5, "parent": 146}, {"command": 4, "file": 5, "line": 103, "parent": 147}, {"command": 6, "file": 6, "line": 262, "parent": 144}, {"command": 5, "file": 6, "line": 141, "parent": 149}, {"file": 9, "parent": 150}, {"command": 4, "file": 9, "line": 103, "parent": 151}, {"file": 16}, {"command": 1, "file": 16, "line": 2, "parent": 153}, {"file": 3, "parent": 154}, {"command": 3, "file": 3, "line": 90, "parent": 155}, {"command": 2, "file": 3, "line": 53, "parent": 156}, {"file": 17}, {"command": 1, "file": 17, "line": 2, "parent": 158}, {"file": 3, "parent": 159}, {"command": 3, "file": 3, "line": 90, "parent": 160}, {"command": 2, "file": 3, "line": 53, "parent": 161}, {"command": 1, "file": 3, "line": 1, "parent": 160}, {"file": 2, "parent": 163}, {"command": 1, "file": 2, "line": 81, "parent": 164}, {"file": 8, "parent": 165}, {"command": 5, "file": 8, "line": 33, "parent": 166}, {"file": 7, "parent": 167}, {"command": 5, "file": 7, "line": 610, "parent": 168}, {"file": 6, "parent": 169}, {"command": 6, "file": 6, "line": 262, "parent": 170}, {"command": 5, "file": 6, "line": 141, "parent": 171}, {"file": 5, "parent": 172}, {"command": 4, "file": 5, "line": 103, "parent": 173}, {"command": 6, "file": 6, "line": 262, "parent": 170}, {"command": 5, "file": 6, "line": 141, "parent": 175}, {"file": 9, "parent": 176}, {"command": 4, "file": 9, "line": 103, "parent": 177}, {"file": 18}, {"command": 1, "file": 18, "line": 3, "parent": 179}, {"file": 3, "parent": 180}, {"command": 3, "file": 3, "line": 78, "parent": 181}, {"command": 2, "file": 3, "line": 53, "parent": 182}, {"command": 1, "file": 3, "line": 1, "parent": 181}, {"file": 2, "parent": 184}, {"command": 1, "file": 2, "line": 81, "parent": 185}, {"file": 8, "parent": 186}, {"command": 5, "file": 8, "line": 33, "parent": 187}, {"file": 7, "parent": 188}, {"command": 5, "file": 7, "line": 610, "parent": 189}, {"file": 6, "parent": 190}, {"command": 6, "file": 6, "line": 262, "parent": 191}, {"command": 5, "file": 6, "line": 141, "parent": 192}, {"file": 5, "parent": 193}, {"command": 4, "file": 5, "line": 103, "parent": 194}, {"command": 6, "file": 6, "line": 262, "parent": 191}, {"command": 5, "file": 6, "line": 141, "parent": 196}, {"file": 9, "parent": 197}, {"command": 4, "file": 9, "line": 103, "parent": 198}, {"command": 3, "file": 3, "line": 84, "parent": 181}, {"command": 2, "file": 3, "line": 53, "parent": 200}, {"command": 3, "file": 3, "line": 84, "parent": 181}, {"command": 2, "file": 3, "line": 53, "parent": 202}, {"command": 3, "file": 3, "line": 84, "parent": 181}, {"command": 2, "file": 3, "line": 53, "parent": 204}, {"command": 3, "file": 3, "line": 84, "parent": 181}, {"command": 2, "file": 3, "line": 53, "parent": 206}, {"file": 19}, {"command": 1, "file": 19, "line": 2, "parent": 208}, {"file": 3, "parent": 209}, {"command": 3, "file": 3, "line": 90, "parent": 210}, {"command": 2, "file": 3, "line": 53, "parent": 211}, {"command": 1, "file": 3, "line": 1, "parent": 210}, {"file": 2, "parent": 213}, {"command": 1, "file": 2, "line": 81, "parent": 214}, {"file": 8, "parent": 215}, {"command": 5, "file": 8, "line": 33, "parent": 216}, {"file": 7, "parent": 217}, {"command": 5, "file": 7, "line": 610, "parent": 218}, {"file": 6, "parent": 219}, {"command": 6, "file": 6, "line": 262, "parent": 220}, {"command": 5, "file": 6, "line": 141, "parent": 221}, {"file": 5, "parent": 222}, {"command": 4, "file": 5, "line": 103, "parent": 223}, {"command": 6, "file": 6, "line": 262, "parent": 220}, {"command": 5, "file": 6, "line": 141, "parent": 225}, {"file": 9, "parent": 226}, {"command": 4, "file": 9, "line": 103, "parent": 227}, {"command": 1, "file": 5, "line": 53, "parent": 21}, {"file": 20, "parent": 229}, {"command": 4, "file": 20, "line": 101, "parent": 230}, {"command": 1, "file": 3, "line": 1, "parent": 155}, {"file": 2, "parent": 232}, {"command": 1, "file": 2, "line": 81, "parent": 233}, {"file": 8, "parent": 234}, {"command": 5, "file": 8, "line": 33, "parent": 235}, {"file": 7, "parent": 236}, {"command": 5, "file": 7, "line": 610, "parent": 237}, {"file": 6, "parent": 238}, {"command": 6, "file": 6, "line": 262, "parent": 239}, {"command": 5, "file": 6, "line": 141, "parent": 240}, {"file": 5, "parent": 241}, {"command": 4, "file": 5, "line": 103, "parent": 242}, {"command": 6, "file": 6, "line": 262, "parent": 239}, {"command": 5, "file": 6, "line": 141, "parent": 244}, {"file": 9, "parent": 245}, {"command": 4, "file": 9, "line": 103, "parent": 246}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 248}, {"command": 7, "file": 2, "line": 54, "parent": 249}, {"command": 1, "file": 2, "line": 81, "parent": 249}, {"file": 21, "parent": 251}, {"command": 7, "file": 21, "line": 8, "parent": 252}, {"command": 1, "file": 2, "line": 81, "parent": 249}, {"file": 22, "parent": 254}, {"command": 7, "file": 22, "line": 29, "parent": 255}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /Zi /O2 /Ob1 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 250, "path": "D:/newfuxios/Analysis_Robot/App/include"}, {"backtrace": 253, "path": "C:/opt/libmodbus/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/restInterfaceDriver/include"}, {"backtrace": 5, "path": "C:/opt/glog/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/robotDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/moistureAnalyzerDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/balanceDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/plcDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/aixsDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/algorithms/pouringControl/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/algorithms/coordinateTransform/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/algorithms/tcpPositionMaintain/include"}, {"backtrace": 256, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b"}, {"backtrace": 5, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115"}, {"backtrace": 5, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c"}, {"backtrace": 5, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf"}, {"backtrace": 5, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e"}, {"backtrace": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16"}, {"backtrace": 5, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455"}, {"backtrace": 5, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a"}, {"backtrace": 5, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b"}, {"backtrace": 5, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3"}], "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /Zi /O2 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "..\\drivers\\restInterfaceDriver\\RelWithDebInfo\\Analysis_RobotdriversrestInterfaceDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\robotDriver\\RelWithDebInfo\\Analysis_RobotdriversrobotDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\moistureAnalyzerDriver\\RelWithDebInfo\\Analysis_RobotdriversmoistureAnalyzerDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\heatingMagneticStirrerDriver\\RelWithDebInfo\\Analysis_RobotdriversheatingMagneticStirrerDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\balanceDriver\\RelWithDebInfo\\Analysis_RobotdriversbalanceDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\plcDriver\\RelWithDebInfo\\Analysis_RobotdriversplcDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\aixsDriver\\RelWithDebInfo\\Analysis_RobotdriversaixsDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\algorithms\\pouringControl\\RelWithDebInfo\\Analysis_RobotalgorithmspouringControl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicommon\\RelWithDebInfo\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 64, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 85, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 89, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 106, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 110, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 94, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\balanceDriver\\RelWithDebInfo\\Analysis_RobotdriversbalanceDriver.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 127, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 131, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 115, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\drivers\\aixsDriver\\RelWithDebInfo\\Analysis_RobotdriversaixsDriver.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 148, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 152, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 136, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "..\\algorithms\\coordinateTransform\\RelWithDebInfo\\Analysis_RobotalgorithmscoordinateTransform.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 174, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 178, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 162, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "..\\algorithms\\tcpPositionMaintain\\RelWithDebInfo\\Analysis_RobotalgorithmstcpPositionMaintain.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicommon\\RelWithDebInfo\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 195, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 199, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 183, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 201, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 203, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 205, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 207, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 224, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 228, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 212, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 231, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 231, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 243, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 247, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 157, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Analysis_RobotApp", "nameOnDisk": "Analysis_RobotApp.exe", "paths": {"build": "Analysis_Robot/App", "source": "Analysis_Robot/App"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/AnalysisRobotApp.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/BalanceController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/ConfigManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/ConfigRegistration.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/DeviceManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/DosingController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/FilterController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/FrameController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/HeatingMagneticStirrerController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/ICPEntryController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/MoistureAnalyzerController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/PouringController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/RepoController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/RobotController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/SampleEntryController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/SampleExitController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/ShakerController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/StirController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/TaskHelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/VolumeController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/App/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "Analysis_Robot/App/include/AnalysisRobotApp.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/BalanceController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/ConfigManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/DeviceManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/DosingController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/FilterController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/FrameController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/HeatingMagneticStirrerController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/ICPEntryController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/MoistureAnalyzerController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/PouringController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/RepoController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/RobotController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/SampleEntryController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/SampleExitController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/ShakerController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/StirController.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/TaskHelper.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Analysis_Robot/App/include/VolumeController.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}