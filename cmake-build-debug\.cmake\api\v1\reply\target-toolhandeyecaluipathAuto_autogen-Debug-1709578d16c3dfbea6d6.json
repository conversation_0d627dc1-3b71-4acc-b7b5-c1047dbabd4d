{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["tool/handeyecaluipathAuto/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"backtrace": 0, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 0, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}, {"backtrace": 0, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}], "id": "toolhandeyecaluipathAuto_autogen::@f4fb3041b29f01391299", "isGeneratorProvided": true, "name": "toolhandeyecaluipathAuto_autogen", "paths": {"build": "tool/handeyecaluipathAuto", "source": "tool/handeyecaluipathAuto"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecaluipathAuto/CMakeFiles/toolhandeyecaluipathAuto_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecaluipathAuto/CMakeFiles/toolhandeyecaluipathAuto_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}