/****************************************************************************
** Meta object code from reading C++ file 'LicenseManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../fuxicore/include/management/LicenseManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'LicenseManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_Fuxi__Core__LicenseManager_t {
    QByteArrayData data[20];
    char stringdata0[325];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Fuxi__Core__LicenseManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Fuxi__Core__LicenseManager_t qt_meta_stringdata_Fuxi__Core__LicenseManager = {
    {
QT_MOC_LITERAL(0, 0, 26), // "Fuxi::Core::LicenseManager"
QT_MOC_LITERAL(1, 27, 16), // "licenseValidated"
QT_MOC_LITERAL(2, 44, 0), // ""
QT_MOC_LITERAL(3, 45, 16), // "ValidationResult"
QT_MOC_LITERAL(4, 62, 6), // "result"
QT_MOC_LITERAL(5, 69, 15), // "licenseExpiring"
QT_MOC_LITERAL(6, 85, 13), // "daysRemaining"
QT_MOC_LITERAL(7, 99, 14), // "licenseExpired"
QT_MOC_LITERAL(8, 114, 19), // "featureAccessDenied"
QT_MOC_LITERAL(9, 134, 11), // "featureName"
QT_MOC_LITERAL(10, 146, 17), // "tamperingDetected"
QT_MOC_LITERAL(11, 164, 7), // "details"
QT_MOC_LITERAL(12, 172, 17), // "debuggingDetected"
QT_MOC_LITERAL(13, 190, 25), // "onlineValidationCompleted"
QT_MOC_LITERAL(14, 216, 7), // "success"
QT_MOC_LITERAL(15, 224, 22), // "securityThreatDetected"
QT_MOC_LITERAL(16, 247, 10), // "threatType"
QT_MOC_LITERAL(17, 258, 18), // "checkLicenseExpiry"
QT_MOC_LITERAL(18, 277, 20), // "performSecurityCheck"
QT_MOC_LITERAL(19, 298, 26) // "onOnlineValidationFinished"

    },
    "Fuxi::Core::LicenseManager\0licenseValidated\0"
    "\0ValidationResult\0result\0licenseExpiring\0"
    "daysRemaining\0licenseExpired\0"
    "featureAccessDenied\0featureName\0"
    "tamperingDetected\0details\0debuggingDetected\0"
    "onlineValidationCompleted\0success\0"
    "securityThreatDetected\0threatType\0"
    "checkLicenseExpiry\0performSecurityCheck\0"
    "onOnlineValidationFinished"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Fuxi__Core__LicenseManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       5,    1,   72,    2, 0x06 /* Public */,
       7,    0,   75,    2, 0x06 /* Public */,
       8,    1,   76,    2, 0x06 /* Public */,
      10,    1,   79,    2, 0x06 /* Public */,
      12,    0,   82,    2, 0x06 /* Public */,
      13,    1,   83,    2, 0x06 /* Public */,
      15,    1,   86,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      17,    0,   89,    2, 0x08 /* Private */,
      18,    0,   90,    2, 0x08 /* Private */,
      19,    0,   91,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Int,    6,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   14,
    QMetaType::Void, QMetaType::QString,   16,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void Fuxi::Core::LicenseManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<LicenseManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->licenseValidated((*reinterpret_cast< ValidationResult(*)>(_a[1]))); break;
        case 1: _t->licenseExpiring((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->licenseExpired(); break;
        case 3: _t->featureAccessDenied((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->tamperingDetected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->debuggingDetected(); break;
        case 6: _t->onlineValidationCompleted((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->securityThreatDetected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->checkLicenseExpiry(); break;
        case 9: _t->performSecurityCheck(); break;
        case 10: _t->onOnlineValidationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (LicenseManager::*)(ValidationResult );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::licenseValidated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::licenseExpiring)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::licenseExpired)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::featureAccessDenied)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::tamperingDetected)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::debuggingDetected)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::onlineValidationCompleted)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (LicenseManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&LicenseManager::securityThreatDetected)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject Fuxi::Core::LicenseManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_Fuxi__Core__LicenseManager.data,
    qt_meta_data_Fuxi__Core__LicenseManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *Fuxi::Core::LicenseManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Fuxi::Core::LicenseManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Fuxi__Core__LicenseManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Fuxi::Core::LicenseManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void Fuxi::Core::LicenseManager::licenseValidated(ValidationResult _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void Fuxi::Core::LicenseManager::licenseExpiring(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void Fuxi::Core::LicenseManager::licenseExpired()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void Fuxi::Core::LicenseManager::featureAccessDenied(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void Fuxi::Core::LicenseManager::tamperingDetected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void Fuxi::Core::LicenseManager::debuggingDetected()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void Fuxi::Core::LicenseManager::onlineValidationCompleted(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void Fuxi::Core::LicenseManager::securityThreatDetected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
