"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_BUILD_TYPE=Debug "-DCMAKE_MAKE_PROGRAM=C:/Program Files/JetBrains/CLion 2025.1.3/bin/ninja/win/x64/ninja.exe" -G Ninja -S D:\newfuxios -B D:\newfuxios\cmake-build-debug
CMake Warning (dev) in CMakeLists.txt:
  No project() command is present.  The top-level CMakeLists.txt file must
  contain a literal, direct call to the project() command.  Add a line of
  code such as

    project(ProjectName)

  near the top of the file, but after cmake_minimum_required().

  CMake is pretending there is a "project(Project)" command on the first
  line.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) in CMakeLists.txt:
  cmake_minimum_required() should be called prior to this top-level project()
  call.  Please see the cmake-commands(7) manual for usage documentation of
  both commands.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- ABBDrawPicture
-- Analysis_Robot/App
-- Analysis_Robot/algorithms/coordinateTransform
-- Analysis_Robot/algorithms/pouringControl
-- Analysis_Robot/algorithms/tcpPositionMaintain
-- Analysis_Robot/drivers/aixsDriver
-- Analysis_Robot/drivers/balanceDriver
-- Analysis_Robot/drivers/heatingMagneticStirrerDriver
-- Analysis_Robot/drivers/moistureAnalyzerDriver
-- Analysis_Robot/drivers/plcDriver
-- Analysis_Robot/drivers/restInterfaceDriver
-- Analysis_Robot/drivers/robotDriver
-- Analysis_Robot/test/balanceDriverTest
-- Analysis_Robot/test/balanceTest
-- Analysis_Robot/test/heaterApiTest
-- Analysis_Robot/test/heatingMagneticStirrerDriver
-- Analysis_Robot/test/moistureAnalyzerDriverTest
-- 
-- MJServer/APP
-- MJServer_Refactor/App
-- MJServer_Refactor/Library
-- MJServer_Refactor/Test/phase1_test
-- MJServer_Refactor/Test/simple_abb_client
-- MJServer_Refactor/Test/simple_feeder_client
-- RoboticLaserMarking/AbbDriver
-- RoboticLaserMarking/LicenseGenerator
-- RoboticLaserMarking/RFIDDriver
-- RoboticLaserMarking/Test/abbsocket
-- RoboticLaserMarking/Test/laser
-- RoboticLaserMarking/Test/laserUI
-- RoboticLaserMarking/Test/rfiddriver
-- RoboticLaserMarking/Test/rfidserver
-- RoboticLaserMarking/UI
-- RoboticLaserMarking/laserDriver
-- RoboticLaserMarking/laserDriverSim
-- Test/test_abb_socket
-- Test/test_config_manager
-- Test/test_csv
-- Test/test_event_listener
-- Test/test_executor
-- Test/test_executor_context
-- Test/test_fa2204n_balance
-- Test/test_fa2204n_balance_basic
-- Test/test_fileutil
-- Test/test_json
-- Test/test_license_manager
-- Test/test_license_ui
-- Test/test_micro_dosing
-- Test/test_network
-- Test/test_serial
-- Test/test_service_container
-- Test/test_socket
-- Test/test_sqlite
-- Test/test_taskflow
-- Test/test_twoaixsrobot
-- Test/test_xml
-- fuxicommon
-- fuxicore
-- hardwaredriver/AuboArcsDriver
-- hardwaredriver/AuboDriver
-- hardwaredriver/ElectricGripperDriver
-- hardwaredriver/HikVisionCamera
-- hardwaredriver/LabelPrinter
-- hardwaredriver/MettlerBalance
-- hardwaredriver/OpcDa
-- hardwaredriver/OpcUa
-- hardwaredriver/abbRobotDriver
-- hardwaredriver/agilerobotDriver
-- hardwaredriver/fairinoDriver
-- hardwaredriver/junduoHandDriver
-- hardwaredriver/modbus
-- hardwaredriver/serial
-- hardwaredriver/socket
-- hardwaredriver/usbcamera
-- tool/calbuild
-- tool/caltest
-- tool/cameraCalibrator
-- tool/communication
-- tool/handeyecal
-- tool/handeyecaltest
-- tool/handeyecalui/handeyecalui
-- tool/handeyecaluipath
-- tool/handeyecaluipathAuto
-- tool/verify_calibration
--  SUBDIRS ABBDrawPicture;Analysis_Robot/App;Analysis_Robot/algorithms/coordinateTransform;Analysis_Robot/algorithms/pouringControl;Analysis_Robot/algorithms/tcpPositionMaintain;Analysis_Robot/drivers/aixsDriver;Analysis_Robot/drivers/balanceDriver;Analysis_Robot/drivers/heatingMagneticStirrerDriver;Analysis_Robot/drivers/moistureAnalyzerDriver;Analysis_Robot/drivers/plcDriver;Analysis_Robot/drivers/restInterfaceDriver;Analysis_Robot/drivers/robotDriver;Analysis_Robot/test/balanceDriverTest;Analysis_Robot/test/balanceTest;Analysis_Robot/test/heaterApiTest;Analysis_Robot/test/heatingMagneticStirrerDriver;Analysis_Robot/test/moistureAnalyzerDriverTest;MJServer/APP;MJServer_Refactor/App;MJServer_Refactor/Library;MJServer_Refactor/Test/phase1_test;MJServer_Refactor/Test/simple_abb_client;MJServer_Refactor/Test/simple_feeder_client;RoboticLaserMarking/AbbDriver;RoboticLaserMarking/LicenseGenerator;RoboticLaserMarking/RFIDDriver;RoboticLaserMarking/Test/abbsocket;RoboticLaserMarking/Test/laser;RoboticLaserMarking/Test/laserUI;RoboticLaserMarking/Test/rfiddriver;RoboticLaserMarking/Test/rfidserver;RoboticLaserMarking/UI;RoboticLaserMarking/laserDriver;RoboticLaserMarking/laserDriverSim;Test/test_abb_socket;Test/test_config_manager;Test/test_csv;Test/test_event_listener;Test/test_executor;Test/test_executor_context;Test/test_fa2204n_balance;Test/test_fa2204n_balance_basic;Test/test_fileutil;Test/test_json;Test/test_license_manager;Test/test_license_ui;Test/test_micro_dosing;Test/test_network;Test/test_serial;Test/test_service_container;Test/test_socket;Test/test_sqlite;Test/test_taskflow;Test/test_twoaixsrobot;Test/test_xml;fuxicommon;fuxicore;hardwaredriver/AuboArcsDriver;hardwaredriver/AuboDriver;hardwaredriver/ElectricGripperDriver;hardwaredriver/HikVisionCamera;hardwaredriver/LabelPrinter;hardwaredriver/MettlerBalance;hardwaredriver/OpcDa;hardwaredriver/OpcUa;hardwaredriver/abbRobotDriver;hardwaredriver/agilerobotDriver;hardwaredriver/fairinoDriver;hardwaredriver/junduoHandDriver;hardwaredriver/modbus;hardwaredriver/serial;hardwaredriver/socket;hardwaredriver/usbcamera;tool/calbuild;tool/caltest;tool/cameraCalibrator;tool/communication;tool/handeyecal;tool/handeyecaltest;tool/handeyecalui/handeyecalui;tool/handeyecaluipath;tool/handeyecaluipathAuto;tool/verify_calibration
-- add_subdirectory ABBDrawPicture
-- current project name is ABBDrawPicture
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  ABBDrawPicture/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_openssl.cmake
-- openssl_INCLUDE_DIRS: C:/opt/openssl/include
-- openssl_LIBRARIES: C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  ABBDrawPicture/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_xerces-c.cmake
-- xerces-c_INCLUDE_DIR: C:/opt/xerces-c/include
-- xerces-c_LIBRARIES: debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建静态库 ABBDrawPicture
-- 创建Qt静态库 ABBDrawPicture
-- 为 ABBDrawPicture 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- _target debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- 为 ABBDrawPicture 添加依赖库(PUBLIC): Qt5::Core
-- 为 ABBDrawPicture 添加依赖库(PUBLIC): Qt5::Gui
-- 为 ABBDrawPicture 添加依赖库(PUBLIC): Qt5::Sql
-- 为 ABBDrawPicture 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/ABBDrawPicture
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  ABBDrawPicture/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  ABBDrawPicture/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory fuxicommon
-- current project name is fuxicommon
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  fuxicommon/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_openssl.cmake
-- openssl_INCLUDE_DIRS: C:/opt/openssl/include
-- openssl_LIBRARIES: C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  fuxicommon/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_xerces-c.cmake
-- xerces-c_INCLUDE_DIR: C:/opt/xerces-c/include
-- xerces-c_LIBRARIES: debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建静态库 fuxicommon
-- 创建Qt静态库 fuxicommon
-- 为 fuxicommon 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- _target debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- 为 fuxicommon 添加依赖库(PUBLIC): Qt5::Core
-- 为 fuxicommon 添加依赖库(PUBLIC): Qt5::Gui
-- 为 fuxicommon 添加依赖库(PUBLIC): Qt5::Sql
-- 为 fuxicommon 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/fuxicommon
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  fuxicommon/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  fuxicommon/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/algorithms/pouringControl
-- current project name is Analysis_RobotalgorithmspouringControl
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/pouringControl/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/pouringControl/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 Analysis_RobotalgorithmspouringControl
-- 创建静态库 Analysis_RobotalgorithmspouringControl
-- 为 Analysis_RobotalgorithmspouringControl 添加依赖库(PUBLIC): Analysis_RobotalgorithmscoordinateTransform;Analysis_RobotalgorithmstcpPositionMaintain;Analysis_RobotdriversaixsDriver;Analysis_RobotdriversbalanceDriver;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/algorithms/pouringControl
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/pouringControl/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/pouringControl/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/aixsDriver
-- current project name is Analysis_RobotdriversaixsDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/aixsDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/aixsDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_multicard.cmake
-- multicard_INCLUDE_DIR: C:/opt/multicard/include
-- MULTICARD_ROOT_DIR: C:/opt/multicard
-- 创建静态库 Analysis_RobotdriversaixsDriver
-- 创建静态库 Analysis_RobotdriversaixsDriver
-- 为 Analysis_RobotdriversaixsDriver 添加依赖库(PUBLIC): debug;C:/opt/multicard/lib/MultiCard.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/aixsDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/aixsDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/aixsDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/plcDriver
-- current project name is Analysis_RobotdriversplcDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/plcDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/plcDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 Analysis_RobotdriversplcDriver
-- 创建静态库 Analysis_RobotdriversplcDriver
-- 为 Analysis_RobotdriversplcDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/plcDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/plcDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/plcDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/balanceDriver
-- current project name is Analysis_RobotdriversbalanceDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/balanceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/balanceDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 Analysis_RobotdriversbalanceDriver
-- 创建静态库 Analysis_RobotdriversbalanceDriver
-- 为 Analysis_RobotdriversbalanceDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/balanceDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/balanceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/balanceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/heatingMagneticStirrerDriver
-- current project name is Analysis_RobotdriversheatingMagneticStirrerDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 Analysis_RobotdriversheatingMagneticStirrerDriver
-- 创建静态库 Analysis_RobotdriversheatingMagneticStirrerDriver
-- 为 Analysis_RobotdriversheatingMagneticStirrerDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/moistureAnalyzerDriver
-- current project name is Analysis_RobotdriversmoistureAnalyzerDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/moistureAnalyzerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/moistureAnalyzerDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 Analysis_RobotdriversmoistureAnalyzerDriver
-- 创建静态库 Analysis_RobotdriversmoistureAnalyzerDriver
-- 为 Analysis_RobotdriversmoistureAnalyzerDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/moistureAnalyzerDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/moistureAnalyzerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/moistureAnalyzerDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/robotDriver
-- current project name is Analysis_RobotdriversrobotDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/robotDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/robotDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 Analysis_RobotdriversrobotDriver
-- 创建静态库 Analysis_RobotdriversrobotDriver
-- 为 Analysis_RobotdriversrobotDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/robotDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/robotDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/robotDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/drivers/restInterfaceDriver
-- current project name is Analysis_RobotdriversrestInterfaceDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/restInterfaceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/drivers/restInterfaceDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 Analysis_RobotdriversrestInterfaceDriver
-- 创建静态库 Analysis_RobotdriversrestInterfaceDriver
-- 为 Analysis_RobotdriversrestInterfaceDriver 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/drivers/restInterfaceDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/restInterfaceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/drivers/restInterfaceDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/App
-- current project name is Analysis_RobotApp
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- 创建可执行文件 Analysis_RobotApp
-- 为 Analysis_RobotApp 添加依赖库(PUBLIC): Analysis_RobotdriversrestInterfaceDriver;Analysis_RobotdriversrobotDriver;Analysis_RobotdriversmoistureAnalyzerDriver;Analysis_RobotdriversheatingMagneticStirrerDriver;Analysis_RobotdriversbalanceDriver;Analysis_RobotdriversplcDriver;Analysis_RobotdriversaixsDriver;Analysis_RobotalgorithmspouringControl;RTTR::Core;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
CMake Warning (dev) at builder/cmake/executable.cmake:80 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/App/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/algorithms/coordinateTransform
-- current project name is Analysis_RobotalgorithmscoordinateTransform
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/coordinateTransform/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/coordinateTransform/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 Analysis_RobotalgorithmscoordinateTransform
-- 创建静态库 Analysis_RobotalgorithmscoordinateTransform
-- 为 Analysis_RobotalgorithmscoordinateTransform 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/algorithms/coordinateTransform
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/coordinateTransform/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/coordinateTransform/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/algorithms/tcpPositionMaintain
-- current project name is Analysis_RobotalgorithmstcpPositionMaintain
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/tcpPositionMaintain/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  Analysis_Robot/algorithms/tcpPositionMaintain/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 Analysis_RobotalgorithmstcpPositionMaintain
-- 创建静态库 Analysis_RobotalgorithmstcpPositionMaintain
-- 为 Analysis_RobotalgorithmstcpPositionMaintain 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/Analysis_Robot/algorithms/tcpPositionMaintain
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/tcpPositionMaintain/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Analysis_Robot/algorithms/tcpPositionMaintain/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Analysis_Robot/test/balanceDriverTest
-- current project name is Analysis_RobottestbalanceDriverTest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Analysis_Robot/test/balanceDriverTest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 Analysis_RobottestbalanceDriverTest
-- 为 Analysis_RobottestbalanceDriverTest 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;Analysis_RobotdriversbalanceDriver;C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Analysis_Robot/test/balanceTest
-- current project name is Analysis_RobottestbalanceTest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Analysis_Robot/test/balanceTest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Analysis_RobottestbalanceTest
-- 为 Analysis_RobottestbalanceTest 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Analysis_Robot/test/heaterApiTest
-- current project name is Analysis_RobottestheaterApiTest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Analysis_Robot/test/heaterApiTest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Analysis_RobottestheaterApiTest
-- 为 Analysis_RobottestheaterApiTest 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Analysis_Robot/test/heatingMagneticStirrerDriver
-- current project name is Analysis_RobottestheatingMagneticStirrerDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Analysis_Robot/test/heatingMagneticStirrerDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 Analysis_RobottestheatingMagneticStirrerDriver
-- 为 Analysis_RobottestheatingMagneticStirrerDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;Analysis_RobotdriversheatingMagneticStirrerDriver;C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Analysis_Robot/test/moistureAnalyzerDriverTest
-- current project name is Analysis_RobottestmoistureAnalyzerDriverTest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Analysis_Robot/test/moistureAnalyzerDriverTest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Analysis_RobottestmoistureAnalyzerDriverTest
-- 为 Analysis_RobottestmoistureAnalyzerDriverTest 添加依赖库(PUBLIC): Analysis_RobotdriversmoistureAnalyzerDriver;C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory MJServer/APP
-- current project name is MJServerAPP
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 MJServerAPP
-- 为 MJServerAPP 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServerAPP 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServerAPP 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServerAPP 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServerAPP 添加依赖库(PUBLIC): Qt5::Widgets
CMake Warning (dev) at builder/cmake/executable.cmake:39 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  MJServer/APP/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Using manual deployment for MJServerAPP
-- Manual Qt deployment for MJServerAPP
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory MJServer_Refactor/Library
-- current project name is MJServer_RefactorLibrary
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- include D:/newfuxios/builder/cmake/add_jsoncpp.cmake
-- jsoncpp_INCLUDE_DIRS: C:/opt/jsoncpp/include
-- jsoncpp_LIBRARIES: debug;C:/opt/jsoncpp/lib/jsoncpp.lib;debug;C:/opt/jsoncpp/lib/jsoncpp_static.lib;optimized;C:/opt/jsoncpp/lib/jsoncpp.lib;optimized;C:/opt/jsoncpp/lib/jsoncpp_static.lib
-- 创建静态库 MJServer_RefactorLibrary
-- 创建Qt静态库 MJServer_RefactorLibrary
-- 为 MJServer_RefactorLibrary 添加依赖库(PUBLIC): debug;C:/opt/jsoncpp/lib/jsoncpp.lib;debug;C:/opt/jsoncpp/lib/jsoncpp_static.lib;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- _target debug;C:/opt/jsoncpp/lib/jsoncpp.lib;debug;C:/opt/jsoncpp/lib/jsoncpp_static.lib;optimized;C:/opt/jsoncpp/lib/jsoncpp.lib;optimized;C:/opt/jsoncpp/lib/jsoncpp_static.lib;debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServer_RefactorLibrary 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServer_RefactorLibrary 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServer_RefactorLibrary 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServer_RefactorLibrary 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/MJServer_Refactor/Library
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  MJServer_Refactor/Library/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  MJServer_Refactor/Library/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory MJServer_Refactor/App
-- current project name is MJServer_RefactorApp
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 MJServer_RefactorApp
-- 为 MJServer_RefactorApp 添加依赖库(PUBLIC): MJServer_RefactorLibrary;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServer_RefactorApp 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServer_RefactorApp 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServer_RefactorApp 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServer_RefactorApp 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for MJServer_RefactorApp
-- Manual Qt deployment for MJServer_RefactorApp
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory MJServer_Refactor/Test/phase1_test
-- current project name is MJServer_RefactorTestphase1_test
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 MJServer_RefactorTestphase1_test
-- 为 MJServer_RefactorTestphase1_test 添加依赖库(PUBLIC): MJServer_RefactorLibrary;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServer_RefactorTestphase1_test 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServer_RefactorTestphase1_test 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServer_RefactorTestphase1_test 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServer_RefactorTestphase1_test 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for MJServer_RefactorTestphase1_test
-- Manual Qt deployment for MJServer_RefactorTestphase1_test
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory MJServer_Refactor/Test/simple_abb_client
-- current project name is MJServer_RefactorTestsimple_abb_client
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 MJServer_RefactorTestsimple_abb_client
-- 为 MJServer_RefactorTestsimple_abb_client 添加依赖库(PUBLIC): MJServer_RefactorLibrary;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServer_RefactorTestsimple_abb_client 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServer_RefactorTestsimple_abb_client 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServer_RefactorTestsimple_abb_client 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServer_RefactorTestsimple_abb_client 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for MJServer_RefactorTestsimple_abb_client
-- Manual Qt deployment for MJServer_RefactorTestsimple_abb_client
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory MJServer_Refactor/Test/simple_feeder_client
-- current project name is MJServer_RefactorTestsimple_feeder_client
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 MJServer_RefactorTestsimple_feeder_client
-- 为 MJServer_RefactorTestsimple_feeder_client 添加依赖库(PUBLIC): MJServer_RefactorLibrary;debug;C:/opt/libmodbus/lib/modbus.lib;fuxicommon
-- 为 MJServer_RefactorTestsimple_feeder_client 添加依赖库(PUBLIC): Qt5::Core
-- 为 MJServer_RefactorTestsimple_feeder_client 添加依赖库(PUBLIC): Qt5::Gui
-- 为 MJServer_RefactorTestsimple_feeder_client 添加依赖库(PUBLIC): Qt5::Sql
-- 为 MJServer_RefactorTestsimple_feeder_client 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for MJServer_RefactorTestsimple_feeder_client
-- Manual Qt deployment for MJServer_RefactorTestsimple_feeder_client
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/AbbDriver
-- current project name is RoboticLaserMarkingAbbDriver
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  RoboticLaserMarking/AbbDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 RoboticLaserMarkingAbbDriver
-- 创建静态库 RoboticLaserMarkingAbbDriver
-- 为 RoboticLaserMarkingAbbDriver 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/RoboticLaserMarking/AbbDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/AbbDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/AbbDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory RoboticLaserMarking/LicenseGenerator
-- current project name is RoboticLaserMarkingLicenseGenerator
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/LicenseGenerator/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 RoboticLaserMarkingLicenseGenerator
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Charts
-- 为 RoboticLaserMarkingLicenseGenerator 添加依赖库(PUBLIC): Qt5::Network
CMake Warning (dev) at builder/cmake/executable.cmake:39 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/LicenseGenerator/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Using manual deployment for RoboticLaserMarkingLicenseGenerator
-- Manual Qt deployment for RoboticLaserMarkingLicenseGenerator
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/RFIDDriver
-- current project name is RoboticLaserMarkingRFIDDriver
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  RoboticLaserMarking/RFIDDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 RoboticLaserMarkingRFIDDriver
-- 创建静态库 RoboticLaserMarkingRFIDDriver
-- 为 RoboticLaserMarkingRFIDDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/RoboticLaserMarking/RFIDDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/RFIDDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/RFIDDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory RoboticLaserMarking/laserDriver
-- current project name is RoboticLaserMarkinglaserDriver
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  RoboticLaserMarking/laserDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_laserControlFrame.cmake
-- Using single LaserControlFrame library for all configurations
-- LaserControlFrame found at: C:/opt/laserControlFrame
-- laserControlFrame_INCLUDE_DIRS: C:/opt/laserControlFrame/include
-- laserControlFrame_LIBRARIES: C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib
-- 创建静态库 RoboticLaserMarkinglaserDriver
-- 创建静态库 RoboticLaserMarkinglaserDriver
-- 为 RoboticLaserMarkinglaserDriver 添加依赖库(PUBLIC): C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib;C:/opt/glog/lib/glogd.lib
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/RoboticLaserMarking/laserDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/laserDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/laserDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory RoboticLaserMarking/Test/abbsocket
-- current project name is RoboticLaserMarkingTestabbsocket
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/Test/abbsocket/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 RoboticLaserMarkingTestabbsocket
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): RoboticLaserMarkingRFIDDriver;RoboticLaserMarkinglaserDriver;RoboticLaserMarkingAbbDriver;debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Charts
-- 为 RoboticLaserMarkingTestabbsocket 添加依赖库(PUBLIC): Qt5::Network
-- Using manual deployment for RoboticLaserMarkingTestabbsocket
-- Manual Qt deployment for RoboticLaserMarkingTestabbsocket
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/Test/laser
-- current project name is RoboticLaserMarkingTestlaser
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/Test/laser/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 RoboticLaserMarkingTestlaser
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): RoboticLaserMarkinglaserDriver;debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;fuxicommon
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingTestlaser 添加依赖库(PUBLIC): Qt5::Charts
-- Using manual deployment for RoboticLaserMarkingTestlaser
-- Manual Qt deployment for RoboticLaserMarkingTestlaser
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/Test/laserUI
-- current project name is RoboticLaserMarkingTestlaserUI
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/Test/laserUI/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 RoboticLaserMarkingTestlaserUI
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): RoboticLaserMarkinglaserDriver;debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;fuxicommon
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingTestlaserUI 添加依赖库(PUBLIC): Qt5::Charts
-- Using manual deployment for RoboticLaserMarkingTestlaserUI
-- Manual Qt deployment for RoboticLaserMarkingTestlaserUI
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/Test/rfiddriver
-- current project name is RoboticLaserMarkingTestrfiddriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/Test/rfiddriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 RoboticLaserMarkingTestrfiddriver
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): RoboticLaserMarkingRFIDDriver;debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;fuxicommon
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingTestrfiddriver 添加依赖库(PUBLIC): Qt5::Charts
-- Using manual deployment for RoboticLaserMarkingTestrfiddriver
-- Manual Qt deployment for RoboticLaserMarkingTestrfiddriver
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/Test/rfidserver
-- current project name is RoboticLaserMarkingTestrfidserver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/Test/rfidserver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建可执行文件 RoboticLaserMarkingTestrfidserver
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;fuxicommon
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingTestrfidserver 添加依赖库(PUBLIC): Qt5::Charts
-- Using manual deployment for RoboticLaserMarkingTestrfidserver
-- Manual Qt deployment for RoboticLaserMarkingTestrfidserver
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/UI
-- current project name is RoboticLaserMarkingUI
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  RoboticLaserMarking/UI/CMakeLists.txt:5 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- include D:/newfuxios/builder/cmake/add_laserControlFrame.cmake
-- Using single LaserControlFrame library for all configurations
-- LaserControlFrame found at: C:/opt/laserControlFrame
-- laserControlFrame_INCLUDE_DIRS: C:/opt/laserControlFrame/include
-- laserControlFrame_LIBRARIES: C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib
-- 创建可执行文件 RoboticLaserMarkingUI
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): RoboticLaserMarkingRFIDDriver;RoboticLaserMarkinglaserDriver;RoboticLaserMarkingAbbDriver;C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib;debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;fuxicommon
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Core
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Gui
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Sql
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Charts
-- 为 RoboticLaserMarkingUI 添加依赖库(PUBLIC): Qt5::Network
CMake Warning (dev) at builder/cmake/executable.cmake:39 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/UI/CMakeLists.txt:5 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Using manual deployment for RoboticLaserMarkingUI
-- Manual Qt deployment for RoboticLaserMarkingUI
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory RoboticLaserMarking/laserDriverSim
-- current project name is RoboticLaserMarkinglaserDriverSim
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  RoboticLaserMarking/laserDriverSim/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_laserControlFrame.cmake
-- Using single LaserControlFrame library for all configurations
-- LaserControlFrame found at: C:/opt/laserControlFrame
-- laserControlFrame_INCLUDE_DIRS: C:/opt/laserControlFrame/include
-- laserControlFrame_LIBRARIES: C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib
-- 创建静态库 RoboticLaserMarkinglaserDriverSim
-- 创建静态库 RoboticLaserMarkinglaserDriverSim
-- 为 RoboticLaserMarkinglaserDriverSim 添加依赖库(PUBLIC): C:/opt/laserControlFrame/lib/LaserControlFrameSDK.lib;C:/opt/glog/lib/glogd.lib
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/RoboticLaserMarking/laserDriverSim
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/laserDriverSim/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  RoboticLaserMarking/laserDriverSim/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory fuxicore
-- current project name is fuxicore
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  fuxicore/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_openssl.cmake
-- openssl_INCLUDE_DIRS: C:/opt/openssl/include
-- openssl_LIBRARIES: C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  fuxicore/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_xerces-c.cmake
-- xerces-c_INCLUDE_DIR: C:/opt/xerces-c/include
-- xerces-c_LIBRARIES: debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建静态库 fuxicore
-- 创建Qt静态库 fuxicore
-- 为 fuxicore 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- _target debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;RTTR::Core;C:/opt/glog/lib/glogd.lib
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::Core
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::Gui
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::Sql
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::Network
-- 为 fuxicore 添加依赖库(PUBLIC): Qt5::SerialPort
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/fuxicore
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  fuxicore/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  fuxicore/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/abbRobotDriver
-- current project name is hardwaredriverabbRobotDriver
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/abbRobotDriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- include D:/newfuxios/builder/cmake/add_abbrws.cmake
-- abbrws_INCLUDE_DIR: C:/opt/abbrws/include
-- poco_INCLUDE_DIR: C:/opt/poco/x64-Debug/include
-- 创建静态库 hardwaredriverabbRobotDriver
-- 创建Qt静态库 hardwaredriverabbRobotDriver
-- 为 hardwaredriverabbRobotDriver 添加依赖库(PUBLIC): debug;C:/opt/poco/x64-Debug/lib/PocoFoundationd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoXMLd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoNetd.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoFoundation.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoXML.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoNet.lib;C:/opt/abbrws/lib/abb_librws.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;hardwaredriversocket;fuxicommon;C:/opt/glog/lib/glogd.lib
-- _target debug;C:/opt/poco/x64-Debug/lib/PocoFoundationd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoXMLd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoNetd.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoFoundation.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoXML.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoNet.lib;C:/opt/abbrws/lib/abb_librws.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;hardwaredriversocket;fuxicommon;C:/opt/glog/lib/glogd.lib
-- 为 hardwaredriverabbRobotDriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverabbRobotDriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverabbRobotDriver 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/abbRobotDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/abbRobotDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/abbRobotDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory Test/test_abb_socket
-- current project name is Testtest_abb_socket
-- FOUND fuxicore as target 
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_abb_socket/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- include D:/newfuxios/builder/cmake/add_abbrws.cmake
-- abbrws_INCLUDE_DIR: C:/opt/abbrws/include
-- poco_INCLUDE_DIR: C:/opt/poco/x64-Debug/include
-- 创建可执行文件 Testtest_abb_socket
-- 为 Testtest_abb_socket 添加依赖库(PUBLIC): debug;C:/opt/poco/x64-Debug/lib/PocoFoundationd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoXMLd.lib;debug;C:/opt/poco/x64-Debug/lib/PocoNetd.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoFoundation.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoXML.lib;optimized;C:/opt/poco/x64-Debug/lib/PocoNet.lib;C:/opt/abbrws/lib/abb_librws.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;hardwaredriverabbRobotDriver;C:/opt/glog/lib/glogd.lib;fuxicommon;fuxicore
-- 为 Testtest_abb_socket 添加依赖库(PUBLIC): Qt5::Core
-- 为 Testtest_abb_socket 添加依赖库(PUBLIC): Qt5::Gui
-- 为 Testtest_abb_socket 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for Testtest_abb_socket
-- Manual Qt deployment for Testtest_abb_socket
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory Test/test_config_manager
-- current project name is Testtest_config_manager
-- FOUND fuxicommon as target 
-- FOUND fuxicore as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_config_manager/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_config_manager
-- 为 Testtest_config_manager 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicore;fuxicommon
-- add_subdirectory Test/test_csv
-- current project name is Testtest_csv
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_csv/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_csv
-- 为 Testtest_csv 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_event_listener
-- current project name is Testtest_event_listener
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_event_listener/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_event_listener
-- 为 Testtest_event_listener 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_executor
-- current project name is Testtest_executor
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_executor/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_executor
-- 为 Testtest_executor 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_executor_context
-- current project name is Testtest_executor_context
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_executor_context/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_executor_context
-- 为 Testtest_executor_context 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_fa2204n_balance
-- current project name is Testtest_fa2204n_balance
-- include D:/newfuxios/builder/cmake/add_hardwaredriverFA2204NBalance.cmake
-- hardwaredriverFA2204NBalance_INCLUDE_DIRS: D:/newfuxios/hardwaredriver/FA2204NBalance/include
-- hardwaredriverFA2204NBalance_LIBRARIES: hardwaredriverFA2204NBalance
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_fa2204n_balance/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_fa2204n_balance
-- 为 Testtest_fa2204n_balance 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;debug;C:/opt/libmodbus/lib/modbus.lib;hardwaredriverFA2204NBalance
-- add_subdirectory Test/test_fa2204n_balance_basic
-- current project name is Testtest_fa2204n_balance_basic
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_fa2204n_balance_basic/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_fa2204n_balance_basic
-- 为 Testtest_fa2204n_balance_basic 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib
-- add_subdirectory Test/test_fileutil
-- current project name is Testtest_fileutil
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_fileutil/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_fileutil
-- 为 Testtest_fileutil 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_json
-- current project name is Testtest_json
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_json/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_json
-- 为 Testtest_json 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_license_manager
-- current project name is Testtest_license_manager
-- FOUND fuxicommon as target 
-- FOUND fuxicore as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_license_manager/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_license_manager
-- 为 Testtest_license_manager 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicore;fuxicommon
-- 为 Testtest_license_manager 添加依赖库(PUBLIC): Qt5::Core
-- 为 Testtest_license_manager 添加依赖库(PUBLIC): Qt5::Gui
-- 为 Testtest_license_manager 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 Testtest_license_manager 添加依赖库(PUBLIC): Qt5::Test
-- Using manual deployment for Testtest_license_manager
-- Manual Qt deployment for Testtest_license_manager
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory Test/test_license_ui
-- current project name is Testtest_license_ui
-- FOUND fuxicommon as target 
-- FOUND fuxicore as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_license_ui/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_license_ui
-- 为 Testtest_license_ui 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicore;fuxicommon
-- 为 Testtest_license_ui 添加依赖库(PUBLIC): Qt5::Core
-- 为 Testtest_license_ui 添加依赖库(PUBLIC): Qt5::Gui
-- 为 Testtest_license_ui 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 Testtest_license_ui 添加依赖库(PUBLIC): Qt5::Test
CMake Warning (dev) at builder/cmake/executable.cmake:39 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  Test/test_license_ui/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Using manual deployment for Testtest_license_ui
-- Manual Qt deployment for Testtest_license_ui
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory Test/test_micro_dosing
-- add_subdirectory Test/test_network
-- current project name is Testtest_network
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_openssl.cmake
-- openssl_INCLUDE_DIRS: C:/opt/openssl/include
-- openssl_LIBRARIES: C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32
-- 创建可执行文件 Testtest_network
-- 为 Testtest_network 添加依赖库(PUBLIC): C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;fuxicommon
-- add_subdirectory Test/test_serial
-- current project name is Testtest_serial
-- FOUND fuxicore as target 
-- 创建可执行文件 Testtest_serial
-- 为 Testtest_serial 添加依赖库(PUBLIC): fuxicore
-- add_subdirectory Test/test_service_container
-- current project name is Testtest_service_container
-- FOUND fuxicommon as target 
-- FOUND fuxicore as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_service_container/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_service_container
-- 为 Testtest_service_container 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicore;fuxicommon
-- add_subdirectory Test/test_socket
-- current project name is Testtest_socket
-- FOUND fuxicore as target 
-- 创建可执行文件 Testtest_socket
-- 为 Testtest_socket 添加依赖库(PUBLIC): fuxicore
-- add_subdirectory Test/test_sqlite
-- current project name is Testtest_sqlite
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_sqlite/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_sqlite
-- 为 Testtest_sqlite 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_taskflow
-- current project name is Testtest_taskflow
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_taskflow.cmake
-- TASKFLOW_INCLUDE_DIRS: C:/opt/taskflow-3.10.0/include
-- TASKFLOW_ROOT: C:/opt/taskflow-3.10.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_taskflow/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_taskflow
-- 为 Testtest_taskflow 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory Test/test_twoaixsrobot
-- current project name is Testtest_twoaixsrobot
-- include D:/newfuxios/builder/cmake/add_xerces-c.cmake
-- xerces-c_INCLUDE_DIR: C:/opt/xerces-c/include
-- xerces-c_LIBRARIES: debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- FOUND fuxicore as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_twoaixsrobot/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 Testtest_twoaixsrobot
-- 为 Testtest_twoaixsrobot 添加依赖库(PUBLIC): C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;C:/opt/glog/lib/glogd.lib;fuxicore;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- 为 Testtest_twoaixsrobot 添加依赖库(PUBLIC): Qt5::Core
-- 为 Testtest_twoaixsrobot 添加依赖库(PUBLIC): Qt5::Gui
-- 为 Testtest_twoaixsrobot 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for Testtest_twoaixsrobot
-- Manual Qt deployment for Testtest_twoaixsrobot
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory Test/test_xml
-- current project name is Testtest_xml
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  Test/test_xml/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 Testtest_xml
-- 为 Testtest_xml 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;fuxicommon
-- add_subdirectory hardwaredriver/AuboArcsDriver
-- current project name is hardwaredriverAuboArcsDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/AuboArcsDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/AuboArcsDriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_auboarcs.cmake
-- auboarcs_INCLUDE_DIRS: C:/opt/auboarcs/include
-- auboarcs_LIBRARIES: debug;C:/opt/auboarcs/lib/aubo_sdkd.lib;debug;C:/opt/auboarcs/lib/robot_proxyd.lib;optimized;C:/opt/auboarcs/lib/aubo_sdk.lib;optimized;C:/opt/auboarcs/lib/robot_proxy.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建静态库 hardwaredriverAuboArcsDriver
-- 创建Qt静态库 hardwaredriverAuboArcsDriver
-- 为 hardwaredriverAuboArcsDriver 添加依赖库(PUBLIC): C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/auboarcs/lib/aubo_sdkd.lib;debug;C:/opt/auboarcs/lib/robot_proxyd.lib;optimized;C:/opt/auboarcs/lib/aubo_sdk.lib;optimized;C:/opt/auboarcs/lib/robot_proxy.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- _target C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/auboarcs/lib/aubo_sdkd.lib;debug;C:/opt/auboarcs/lib/robot_proxyd.lib;optimized;C:/opt/auboarcs/lib/aubo_sdk.lib;optimized;C:/opt/auboarcs/lib/robot_proxy.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- 为 hardwaredriverAuboArcsDriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverAuboArcsDriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverAuboArcsDriver 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverAuboArcsDriver 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/AuboArcsDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/AuboArcsDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/AuboArcsDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/AuboDriver
-- current project name is hardwaredriverAuboDriver
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/AuboDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/AuboDriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建静态库 hardwaredriverAuboDriver
-- 创建Qt静态库 hardwaredriverAuboDriver
-- 为 hardwaredriverAuboDriver 添加依赖库(PUBLIC): C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredriverAuboDriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverAuboDriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverAuboDriver 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverAuboDriver 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/AuboDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/AuboDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/AuboDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/ElectricGripperDriver
-- current project name is hardwaredriverElectricGripperDriver
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/ElectricGripperDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/ElectricGripperDriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 hardwaredriverElectricGripperDriver
-- 创建Qt静态库 hardwaredriverElectricGripperDriver
-- 为 hardwaredriverElectricGripperDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredriverElectricGripperDriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverElectricGripperDriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverElectricGripperDriver 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverElectricGripperDriver 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/ElectricGripperDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/ElectricGripperDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/ElectricGripperDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/HikVisionCamera
-- current project name is hardwaredriverHikVisionCamera
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/HikVisionCamera/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/HikVisionCamera/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_hikvision.cmake
-- hikvision_INCLUDE_DIRS: C:/opt/hikvision/include
-- hikvision_LIBRARIES: debug;C:/opt/hikvision/lib/MvCameraControl.lib;optimized;C:/opt/hikvision/lib/MvCameraControl.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建静态库 hardwaredriverHikVisionCamera
-- 创建Qt静态库 hardwaredriverHikVisionCamera
-- 为 hardwaredriverHikVisionCamera 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/hikvision/lib/MvCameraControl.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;debug;C:/opt/hikvision/lib/MvCameraControl.lib;optimized;C:/opt/hikvision/lib/MvCameraControl.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredriverHikVisionCamera 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverHikVisionCamera 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverHikVisionCamera 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverHikVisionCamera 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/HikVisionCamera
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/HikVisionCamera/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/HikVisionCamera/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/LabelPrinter
-- current project name is hardwaredriverLabelPrinter
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/LabelPrinter/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/LabelPrinter/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建静态库 hardwaredriverLabelPrinter
-- 创建Qt静态库 hardwaredriverLabelPrinter
-- 为 hardwaredriverLabelPrinter 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- _target debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- 为 hardwaredriverLabelPrinter 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverLabelPrinter 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverLabelPrinter 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverLabelPrinter 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/LabelPrinter
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/LabelPrinter/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/LabelPrinter/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/MettlerBalance
-- current project name is hardwaredriverMettlerBalance
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/MettlerBalance/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/MettlerBalance/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriverMettlerBalance
-- 创建Qt静态库 hardwaredriverMettlerBalance
-- 为 hardwaredriverMettlerBalance 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredriverMettlerBalance 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverMettlerBalance 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverMettlerBalance 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverMettlerBalance 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/MettlerBalance
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/MettlerBalance/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/MettlerBalance/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/OpcDa
-- current project name is hardwaredriverOpcDa
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/OpcDa/CMakeLists.txt:4 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/OpcDa/CMakeLists.txt:4 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriverOpcDa
-- 创建Qt静态库 hardwaredriverOpcDa
-- 为 hardwaredriverOpcDa 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- 为 hardwaredriverOpcDa 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverOpcDa 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverOpcDa 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverOpcDa 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/OpcDa
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/OpcDa/CMakeLists.txt:4 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/OpcDa/CMakeLists.txt:4 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/OpcUa
-- current project name is hardwaredriverOpcUa
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/OpcUa/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/OpcUa/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriverOpcUa
-- 创建Qt静态库 hardwaredriverOpcUa
-- 为 hardwaredriverOpcUa 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core;fuxicommon
-- 为 hardwaredriverOpcUa 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverOpcUa 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverOpcUa 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverOpcUa 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/OpcUa
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/OpcUa/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/OpcUa/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/socket
-- current project name is hardwaredriversocket
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/socket/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/socket/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriversocket
-- 创建Qt静态库 hardwaredriversocket
-- 为 hardwaredriversocket 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredriversocket 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriversocket 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriversocket 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriversocket 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/socket
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/socket/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/socket/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/agilerobotDriver
-- current project name is hardwaredriveragilerobotDriver
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/agilerobotDriver/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- include D:/newfuxios/builder/cmake/add_agilerobot.cmake
-- agilerobot_INCLUDE_DIRS: C:/opt/DianaApi/include
-- agilerobot_LIBRARIES: debug;C:/opt/DianaApi/lib/DianaApid.lib;debug;C:/opt/DianaApi/lib/GenericAlgorithmd.lib;debug;C:/opt/DianaApi/lib/VersionApid.lib;optimized;C:/opt/DianaApi/lib/DianaApi.lib;optimized;C:/opt/DianaApi/lib/GenericAlgorithm.lib;optimized;C:/opt/DianaApi/lib/VersionApi.lib
-- 创建静态库 hardwaredriveragilerobotDriver
-- 创建Qt静态库 hardwaredriveragilerobotDriver
-- 为 hardwaredriveragilerobotDriver 添加依赖库(PUBLIC): debug;C:/opt/DianaApi/lib/DianaApid.lib;debug;C:/opt/DianaApi/lib/GenericAlgorithmd.lib;debug;C:/opt/DianaApi/lib/VersionApid.lib;optimized;C:/opt/DianaApi/lib/DianaApi.lib;optimized;C:/opt/DianaApi/lib/GenericAlgorithm.lib;optimized;C:/opt/DianaApi/lib/VersionApi.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;fuxicommon;C:/opt/glog/lib/glogd.lib
-- _target debug;C:/opt/DianaApi/lib/DianaApid.lib;debug;C:/opt/DianaApi/lib/GenericAlgorithmd.lib;debug;C:/opt/DianaApi/lib/VersionApid.lib;optimized;C:/opt/DianaApi/lib/DianaApi.lib;optimized;C:/opt/DianaApi/lib/GenericAlgorithm.lib;optimized;C:/opt/DianaApi/lib/VersionApi.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;fuxicommon;C:/opt/glog/lib/glogd.lib
-- 为 hardwaredriveragilerobotDriver 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriveragilerobotDriver 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriveragilerobotDriver 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/agilerobotDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/agilerobotDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/agilerobotDriver/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/fairinoDriver
-- current project name is hardwaredriverfairinoDriver
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/fairinoDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- include D:/newfuxios/builder/cmake/add_libfairino.cmake
-- libfairino_INCLUDE_DIR: C:/opt/libfairino/include
-- libfairino_LIBRARIES: debug;C:/opt/libfairino/lib/vs2017 x86-64/Debug/fairinod.lib;optimized;C:/opt/libfairino/lib/vs2017 x86-64/Release/fairino.lib
-- 创建静态库 hardwaredriverfairinoDriver
-- 创建静态库 hardwaredriverfairinoDriver
-- 为 hardwaredriverfairinoDriver 添加依赖库(PUBLIC): debug;C:/opt/libfairino/lib/vs2017 x86-64/Debug/fairinod.lib;optimized;C:/opt/libfairino/lib/vs2017 x86-64/Release/fairino.lib;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;fuxicommon;C:/opt/glog/lib/glogd.lib
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/fairinoDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/fairinoDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/fairinoDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/junduoHandDriver
-- current project name is hardwaredriverjunduoHandDriver
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/junduoHandDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/junduoHandDriver/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_libmodbus.cmake
-- libmodbus_INCLUDE_DIRS: C:/opt/libmodbus/include
-- libmodbus_LIBRARIES: debug;C:/opt/libmodbus/lib/modbus.lib;optimized;C:/opt/libmodbus/lib/modbus.lib
-- 创建静态库 hardwaredriverjunduoHandDriver
-- 创建静态库 hardwaredriverjunduoHandDriver
-- 为 hardwaredriverjunduoHandDriver 添加依赖库(PUBLIC): debug;C:/opt/libmodbus/lib/modbus.lib;C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/junduoHandDriver
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/junduoHandDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/junduoHandDriver/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/modbus
-- current project name is hardwaredrivermodbus
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/modbus/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/modbus/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredrivermodbus
-- 创建Qt静态库 hardwaredrivermodbus
-- 为 hardwaredrivermodbus 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 hardwaredrivermodbus 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredrivermodbus 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredrivermodbus 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredrivermodbus 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/modbus
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/modbus/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/modbus/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/serial
-- current project name is hardwaredriverserial
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/serial/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/serial/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriverserial
-- 创建Qt静态库 hardwaredriverserial
-- 为 hardwaredriverserial 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon;RTTR::Core
-- _target C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon;RTTR::Core
-- 为 hardwaredriverserial 添加依赖库(PUBLIC): Qt5::Core
-- 为 hardwaredriverserial 添加依赖库(PUBLIC): Qt5::Gui
-- 为 hardwaredriverserial 添加依赖库(PUBLIC): Qt5::Sql
-- 为 hardwaredriverserial 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/serial
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/serial/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/serial/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory hardwaredriver/usbcamera
-- current project name is hardwaredriverusbcamera
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/usbcamera/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  hardwaredriver/usbcamera/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 hardwaredriverusbcamera
-- 创建静态库 hardwaredriverusbcamera
-- 为 hardwaredriverusbcamera 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/hardwaredriver/usbcamera
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/usbcamera/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  hardwaredriver/usbcamera/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory tool/calbuild
-- current project name is toolcalbuild
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/calbuild/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- 创建可执行文件 toolcalbuild
-- 为 toolcalbuild 添加依赖库(PUBLIC): debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- add_subdirectory tool/cameraCalibrator
-- current project name is toolcameraCalibrator
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  tool/cameraCalibrator/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  tool/cameraCalibrator/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建静态库 toolcameraCalibrator
-- 创建静态库 toolcameraCalibrator
-- 为 toolcameraCalibrator 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/tool/cameraCalibrator
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/cameraCalibrator/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/cameraCalibrator/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory tool/caltest
-- current project name is toolcaltest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/caltest/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/caltest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 toolcaltest
-- 为 toolcaltest 添加依赖库(PUBLIC): toolcameraCalibrator;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
CMake Warning (dev) at builder/cmake/executable.cmake:80 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/caltest/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory tool/communication
-- current project name is toolcommunication
-- include D:/newfuxios/builder/cmake/add_rttr.cmake
-- rttr_INCLUDE_DIR: C:/opt/rttr/include
-- rttr_LIBRARIES: RTTR::Core
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  tool/communication/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_xerces-c.cmake
-- xerces-c_INCLUDE_DIR: C:/opt/xerces-c/include
-- xerces-c_LIBRARIES: debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/library.cmake:1 (include)
  tool/communication/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_libevent.cmake
-- libevent_INCLUDE_DIRS: C:/opt/libevent/include
-- libevent_LIBRARIES: debug;C:/opt/libevent/lib/eventd.lib;debug;C:/opt/libevent/lib/event_cored.lib;debug;C:/opt/libevent/lib/event_extrad.lib;debug;C:/opt/libevent/lib/event_openssld.lib;optimized;C:/opt/libevent/lib/event.lib;optimized;C:/opt/libevent/lib/event_core.lib;optimized;C:/opt/libevent/lib/event_extra.lib;optimized;C:/opt/libevent/lib/event_openssl.lib
-- include D:/newfuxios/builder/cmake/add_openssl.cmake
-- openssl_INCLUDE_DIRS: C:/opt/openssl/include
-- openssl_LIBRARIES: C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32
-- 创建静态库 toolcommunication
-- 创建Qt静态库 toolcommunication
-- 为 toolcommunication 添加依赖库(PUBLIC): C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;debug;C:/opt/libevent/lib/eventd.lib;debug;C:/opt/libevent/lib/event_cored.lib;debug;C:/opt/libevent/lib/event_extrad.lib;debug;C:/opt/libevent/lib/event_openssld.lib;optimized;C:/opt/libevent/lib/event.lib;optimized;C:/opt/libevent/lib/event_core.lib;optimized;C:/opt/libevent/lib/event_extra.lib;optimized;C:/opt/libevent/lib/event_openssl.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;C:/opt/glog/lib/glogd.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- _target C:/opt/openssl/lib/libssl.lib;C:/opt/openssl/lib/libcrypto.lib;C:/opt/openssl/lib/jsoncpp.lib;C:/opt/openssl/lib/libcurl.lib;C:/opt/openssl/lib/zlib.lib;ws2_32;crypt32;advapi32;user32;debug;C:/opt/libevent/lib/eventd.lib;debug;C:/opt/libevent/lib/event_cored.lib;debug;C:/opt/libevent/lib/event_extrad.lib;debug;C:/opt/libevent/lib/event_openssld.lib;optimized;C:/opt/libevent/lib/event.lib;optimized;C:/opt/libevent/lib/event_core.lib;optimized;C:/opt/libevent/lib/event_extra.lib;optimized;C:/opt/libevent/lib/event_openssl.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;C:/opt/glog/lib/glogd.lib;debug;C:/opt/xerces-c/lib/xerces-c_3D.lib;optimized;C:/opt/xerces-c/lib/xerces-c_3D.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;RTTR::Core
-- 为 toolcommunication 添加依赖库(PUBLIC): Qt5::Core
-- 为 toolcommunication 添加依赖库(PUBLIC): Qt5::Gui
-- 为 toolcommunication 添加依赖库(PUBLIC): Qt5::Sql
-- 为 toolcommunication 添加依赖库(PUBLIC): Qt5::Widgets
-- CMAKE_CURRENT_SOURCE_DIR library D:/newfuxios/tool/communication
CMake Warning (dev) at builder/cmake/library.cmake:110 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/communication/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at builder/cmake/library.cmake:114 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/communication/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory tool/handeyecal
-- current project name is toolhandeyecal
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecal/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecal/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 toolhandeyecal
-- 为 toolhandeyecal 添加依赖库(PUBLIC): hardwaredriverHikVisionCamera;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;hardwaredriverAuboArcsDriver;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
CMake Warning (dev) at builder/cmake/executable.cmake:80 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
Call Stack (most recent call first):
  tool/handeyecal/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- add_subdirectory tool/handeyecaltest
-- current project name is toolhandeyecaltest
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaltest/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaltest/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 toolhandeyecaltest
-- 为 toolhandeyecaltest 添加依赖库(PUBLIC): hardwaredriverHikVisionCamera;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;hardwaredriverAuboArcsDriver;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- add_subdirectory tool/handeyecalui/handeyecalui
-- current project name is toolhandeyecaluihandeyecalui
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecalui/handeyecalui/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecalui/handeyecalui/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 toolhandeyecaluihandeyecalui
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): hardwaredriverHikVisionCamera;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;hardwaredriverAuboArcsDriver;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Core
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Gui
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Sql
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Widgets
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Charts
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::Charts
-- 为 toolhandeyecaluihandeyecalui 添加依赖库(PUBLIC): Qt5::SerialPort
-- Using manual deployment for toolhandeyecaluihandeyecalui
-- Manual Qt deployment for toolhandeyecaluihandeyecalui
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory tool/handeyecaluipath
-- current project name is toolhandeyecaluipath
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaluipath/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaluipath/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 toolhandeyecaluipath
-- 为 toolhandeyecaluipath 添加依赖库(PUBLIC): hardwaredriverHikVisionCamera;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;hardwaredriverAuboArcsDriver;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- 为 toolhandeyecaluipath 添加依赖库(PUBLIC): Qt5::Core
-- 为 toolhandeyecaluipath 添加依赖库(PUBLIC): Qt5::Gui
-- 为 toolhandeyecaluipath 添加依赖库(PUBLIC): Qt5::Sql
-- 为 toolhandeyecaluipath 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for toolhandeyecaluipath
-- Manual Qt deployment for toolhandeyecaluipath
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory tool/handeyecaluipathAuto
-- current project name is toolhandeyecaluipathAuto
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaluipathAuto/CMakeLists.txt:3 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/handeyecaluipathAuto/CMakeLists.txt:3 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- include D:/newfuxios/builder/cmake/add_aubo.cmake
-- aubo_INCLUDE_DIRS: C:/opt/aubo/include
-- aubo_LIBRARIES: debug;C:/opt/aubo/lib/libserviceinterface.lib;optimized;C:/opt/aubo/lib/libserviceinterface.lib
-- include D:/newfuxios/builder/cmake/add_robwork.cmake
-- RobWork has not been compiled with a Version Number
-- Looking for yaobi - found
-- Looking for pqp - found
-- Looking for fcl - found
-- Looking for sdurw_qhull - found
-- Looking for sdurw_csgjs - found
-- Looking for sdurw_assimp - found
-- Looking for sdurw_zlib - not found (ignored)
-- Looking for sdurw_unzip - found
-- Looking for sdurw_algorithms - found
-- Looking for sdurw_pathplanners - found
-- Looking for sdurw_pathoptimization - found
-- Looking for sdurw_simulation - found
-- Looking for sdurw_opengl - found
-- Looking for sdurw_assembly - found
-- Looking for sdurw_task - found
-- Looking for sdurw_calibration - found
-- Looking for sdurw_csg - found
-- Looking for sdurw_control - found
-- Looking for sdurw_proximitystrategies - found
-- Looking for sdurw - found
-- Looking for sdurw_core - found
-- Looking for sdurw_common - found
-- Looking for sdurw_math - found
-- ROBWORK_LIBRARIES: C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib
-- ROBWORK_INCLUDE_DIRS: C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3;C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12;C:/opt/PCL/3rdParty/Boost/include/boost-1_78;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include;C:/opt/robwork-21.12/robwork-21.12/cmake/../ext
-- 创建可执行文件 toolhandeyecaluipathAuto
-- 为 toolhandeyecaluipathAuto 添加依赖库(PUBLIC): hardwaredriverHikVisionCamera;C:/opt/robwork-21.12/lib/yaobi.lib;C:/opt/robwork-21.12/lib/pqp.lib;C:/opt/robwork-21.12/lib/fcl.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_qhull.lib;C:/opt/robwork-21.12/lib/RobWork/static/sdurw_csgjs.lib;C:/opt/robwork-21.12/lib/sdurw_assimp.lib;C:/opt/robwork-21.12/lib/sdurw_unzip.lib;C:/opt/robwork-21.12/lib/sdurw_algorithms.lib;C:/opt/robwork-21.12/lib/sdurw_pathplanners.lib;C:/opt/robwork-21.12/lib/sdurw_pathoptimization.lib;C:/opt/robwork-21.12/lib/sdurw_simulation.lib;C:/opt/robwork-21.12/lib/sdurw_opengl.lib;C:/opt/robwork-21.12/lib/sdurw_assembly.lib;C:/opt/robwork-21.12/lib/sdurw_task.lib;C:/opt/robwork-21.12/lib/sdurw_calibration.lib;C:/opt/robwork-21.12/lib/sdurw_csg.lib;C:/opt/robwork-21.12/lib/sdurw_control.lib;C:/opt/robwork-21.12/lib/sdurw_proximitystrategies.lib;C:/opt/robwork-21.12/lib/sdurw.lib;C:/opt/robwork-21.12/lib/sdurw_core.lib;C:/opt/robwork-21.12/lib/sdurw_common.lib;C:/opt/robwork-21.12/lib/sdurw_math.lib;opengl32;glu32;optimized;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-x64-1_78.lib;debug;C:/opt/robwork-21.12/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_regex-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_system-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_chrono-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_atomic-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-x64-1_78.lib;C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib;debug;C:/opt/aubo/lib/libserviceinterface.lib;hardwaredriverAuboArcsDriver;C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
-- 为 toolhandeyecaluipathAuto 添加依赖库(PUBLIC): Qt5::Core
-- 为 toolhandeyecaluipathAuto 添加依赖库(PUBLIC): Qt5::Gui
-- 为 toolhandeyecaluipathAuto 添加依赖库(PUBLIC): Qt5::Sql
-- 为 toolhandeyecaluipathAuto 添加依赖库(PUBLIC): Qt5::Widgets
-- Using manual deployment for toolhandeyecaluipathAuto
-- Manual Qt deployment for toolhandeyecaluipathAuto
-- Qt deployment target directory: D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin
-- add_subdirectory tool/verify_calibration
-- current project name is toolverify_calibration
-- FOUND fuxicommon as target 
-- include D:/newfuxios/builder/cmake/add_eigen.cmake
-- EIGEN_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Eigen/eigen3
-- include D:/newfuxios/builder/cmake/add_boost.cmake
CMake Warning (dev) at builder/cmake/add_boost.cmake:33 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/verify_calibration/CMakeLists.txt:2 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- boost_INCLUDE_DIRS: C:/opt/PCL/3rdParty/Boost/include/boost-1_78
-- boost_LIBRARIES: Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options
-- include D:/newfuxios/builder/cmake/add_opencv.cmake
-- OpenCV ARCH: x64
-- OpenCV RUNTIME: vc16
-- OpenCV STATIC: OFF
-- Found OpenCV 4.11.0 in C:/opt/opencv/build/x64/vc16/lib
-- You might need to add C:\opt\opencv\build\x64\vc16\bin to your PATH to be able to run your applications.
-- OpenCV_INCLUDE_DIRS: C:/opt/opencv/build/include
-- OpenCV_LIBRARY_DIRS: 
-- OpenCV_VERSION: 4.11.0
-- include D:/newfuxios/builder/cmake/add_glog.cmake
CMake Warning at builder/cmake/add_glog.cmake:13 (message):
  Only debug version of glog found, using glogd.lib for all configurations
Call Stack (most recent call first):
  builder/cmake/common.cmake:81 (include)
  builder/cmake/executable.cmake:1 (include)
  tool/verify_calibration/CMakeLists.txt:2 (include)


-- GLOG_INCLUDE_DIRS: C:/opt/glog/include
-- GLOG_LIBRARIES: C:/opt/glog/lib/glogd.lib
-- 创建可执行文件 toolverify_calibration
-- 为 toolverify_calibration 添加依赖库(PUBLIC): C:/opt/glog/lib/glogd.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110d.lib;debug;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_world4110.lib;optimized;C:/opt/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;Boost::thread;Boost::program_options;fuxicommon
CMake Error at CMakeLists.txt:12 (add_subdirectory):
  The binary directory

    D:/newfuxios/cmake-build-debug/ABBDrawPicture

  is already used to build a source directory.  It cannot be used to build
  source directory

    D:/newfuxios/ABBDrawPicture

  Specify a unique binary directory name.


-- Configuring incomplete, errors occurred!
