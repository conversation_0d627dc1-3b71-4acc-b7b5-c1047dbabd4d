{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/toolcaltest.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/toolcaltest.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "include_directories"], "files": ["builder/cmake/executable.cmake", "tool/caltest/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "tool/cameraCalibrator/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_glog.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 6}, {"file": 3, "parent": 7}, {"command": 3, "file": 3, "line": 78, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 9}, {"command": 1, "file": 3, "line": 1, "parent": 8}, {"file": 2, "parent": 11}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 8, "parent": 13}, {"command": 5, "file": 8, "line": 33, "parent": 14}, {"file": 7, "parent": 15}, {"command": 5, "file": 7, "line": 610, "parent": 16}, {"file": 6, "parent": 17}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 19}, {"file": 5, "parent": 20}, {"command": 4, "file": 5, "line": 103, "parent": 21}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 23}, {"file": 9, "parent": 24}, {"command": 4, "file": 9, "line": 103, "parent": 25}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 27}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 29}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 31}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 33}, {"file": 10}, {"command": 1, "file": 10, "line": 2, "parent": 35}, {"file": 3, "parent": 36}, {"command": 3, "file": 3, "line": 90, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"command": 1, "file": 3, "line": 1, "parent": 37}, {"file": 2, "parent": 40}, {"command": 1, "file": 2, "line": 81, "parent": 41}, {"file": 8, "parent": 42}, {"command": 5, "file": 8, "line": 33, "parent": 43}, {"file": 7, "parent": 44}, {"command": 5, "file": 7, "line": 610, "parent": 45}, {"file": 6, "parent": 46}, {"command": 6, "file": 6, "line": 262, "parent": 47}, {"command": 5, "file": 6, "line": 141, "parent": 48}, {"file": 5, "parent": 49}, {"command": 4, "file": 5, "line": 103, "parent": 50}, {"command": 6, "file": 6, "line": 262, "parent": 47}, {"command": 5, "file": 6, "line": 141, "parent": 52}, {"file": 9, "parent": 53}, {"command": 4, "file": 9, "line": 103, "parent": 54}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 56}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 8, "parent": 58}, {"command": 5, "file": 8, "line": 33, "parent": 59}, {"file": 7, "parent": 60}, {"command": 5, "file": 7, "line": 610, "parent": 61}, {"file": 6, "parent": 62}, {"command": 6, "file": 6, "line": 262, "parent": 63}, {"command": 5, "file": 6, "line": 141, "parent": 64}, {"file": 5, "parent": 65}, {"command": 1, "file": 5, "line": 53, "parent": 66}, {"file": 11, "parent": 67}, {"command": 4, "file": 11, "line": 101, "parent": 68}, {"command": 4, "file": 5, "line": 103, "parent": 66}, {"command": 6, "file": 6, "line": 262, "parent": 63}, {"command": 5, "file": 6, "line": 141, "parent": 71}, {"file": 9, "parent": 72}, {"command": 4, "file": 9, "line": 103, "parent": 73}, {"command": 7, "file": 2, "line": 54, "parent": 57}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 12, "parent": 76}, {"command": 7, "file": 12, "line": 30, "parent": 77}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 13, "parent": 79}, {"command": 7, "file": 13, "line": 23, "parent": 80}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 14, "parent": 82}, {"command": 7, "file": 14, "line": 16, "parent": 83}, {"command": 7, "file": 8, "line": 40, "parent": 59}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 75, "path": "D:/newfuxios/tool/caltest/include"}, {"backtrace": 78, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 81, "path": "C:/opt/opencv/build/include"}, {"backtrace": 84, "path": "C:/opt/glog/include"}, {"backtrace": 5, "path": "D:/newfuxios/tool/cameraCalibrator/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 85, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "toolcameraCalibrator::@51e97efefc2313866ad5"}], "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "..\\cameraCalibrator\\MinSizeRel\\toolcameraCalibrator.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicommon\\MinSizeRel\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 55, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 69, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 69, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 70, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 74, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "toolcaltest", "nameOnDisk": "toolcaltest.exe", "paths": {"build": "tool/caltest", "source": "tool/caltest"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "tool/caltest/src/CameraCapture.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/caltest/src/caltest.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/caltest/include/CameraCapture.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}