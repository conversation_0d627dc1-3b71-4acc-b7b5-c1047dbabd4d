@echo off
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=2& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Core.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Core.dll || (set FAIL_LINE=3& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=4& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Gui.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Gui.dll || (set FAIL_LINE=5& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=6& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Widgets.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Widgets.dll || (set FAIL_LINE=7& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=8& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Sql.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Sql.dll || (set FAIL_LINE=9& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=10& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Charts.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Charts.dll || (set FAIL_LINE=11& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=12& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/Qt5Network.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/Qt5Network.dll || (set FAIL_LINE=13& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=14& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/platforms || (set FAIL_LINE=15& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/platforms/qwindows.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/platforms/qwindows.dll || (set FAIL_LINE=16& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=17& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/sqldrivers || (set FAIL_LINE=18& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/sqldrivers/qsqlite.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/sqldrivers/qsqlite.dll || (set FAIL_LINE=19& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=20& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats || (set FAIL_LINE=21& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=22& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qgif.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qgif.dll || (set FAIL_LINE=23& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=24& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qico.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qico.dll || (set FAIL_LINE=25& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=26& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qjpeg.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qjpeg.dll || (set FAIL_LINE=27& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=28& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qsvg.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qsvg.dll || (set FAIL_LINE=29& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=30& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qtiff.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qtiff.dll || (set FAIL_LINE=31& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=32& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qwbmp.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qwbmp.dll || (set FAIL_LINE=33& goto :ABORT)
cd /D D:\newfuxios\cmake-build-debug\MJServer_Refactor\Test\simple_abb_client || (set FAIL_LINE=34& goto :ABORT)
"C:\Program Files\JetBrains\CLion 2025.1.3\bin\cmake\win\x64\bin\cmake.exe" -E copy_if_different C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins/imageformats/qwebp.dll D:/newfuxios/cmake-build-debug/../install/x64-install/dev/bin/imageformats/qwebp.dll || (set FAIL_LINE=35& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%