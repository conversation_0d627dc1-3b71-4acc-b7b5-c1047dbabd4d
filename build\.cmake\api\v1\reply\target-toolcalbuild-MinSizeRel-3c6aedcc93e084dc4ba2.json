{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/toolcalbuild.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/toolcalbuild.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "include_directories"], "files": ["builder/cmake/executable.cmake", "tool/calbuild/CMakeLists.txt", "builder/cmake/common.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_opencv.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 73, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 6}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 6, "parent": 8}, {"command": 5, "file": 6, "line": 33, "parent": 9}, {"file": 5, "parent": 10}, {"command": 5, "file": 5, "line": 610, "parent": 11}, {"file": 4, "parent": 12}, {"command": 6, "file": 4, "line": 262, "parent": 13}, {"command": 5, "file": 4, "line": 141, "parent": 14}, {"file": 3, "parent": 15}, {"command": 4, "file": 3, "line": 103, "parent": 16}, {"command": 6, "file": 4, "line": 262, "parent": 13}, {"command": 5, "file": 4, "line": 141, "parent": 18}, {"file": 7, "parent": 19}, {"command": 4, "file": 7, "line": 103, "parent": 20}, {"file": 9}, {"command": 1, "file": 9, "line": 3, "parent": 22}, {"file": 8, "parent": 23}, {"command": 3, "file": 8, "line": 78, "parent": 24}, {"command": 2, "file": 8, "line": 53, "parent": 25}, {"command": 1, "file": 3, "line": 53, "parent": 16}, {"file": 10, "parent": 27}, {"command": 4, "file": 10, "line": 101, "parent": 28}, {"command": 1, "file": 8, "line": 1, "parent": 24}, {"file": 2, "parent": 30}, {"command": 1, "file": 2, "line": 81, "parent": 31}, {"file": 6, "parent": 32}, {"command": 5, "file": 6, "line": 33, "parent": 33}, {"file": 5, "parent": 34}, {"command": 5, "file": 5, "line": 610, "parent": 35}, {"file": 4, "parent": 36}, {"command": 6, "file": 4, "line": 262, "parent": 37}, {"command": 5, "file": 4, "line": 141, "parent": 38}, {"file": 3, "parent": 39}, {"command": 4, "file": 3, "line": 103, "parent": 40}, {"command": 6, "file": 4, "line": 262, "parent": 37}, {"command": 5, "file": 4, "line": 141, "parent": 42}, {"file": 7, "parent": 43}, {"command": 4, "file": 7, "line": 103, "parent": 44}, {"command": 3, "file": 8, "line": 84, "parent": 24}, {"command": 2, "file": 8, "line": 53, "parent": 46}, {"command": 3, "file": 8, "line": 84, "parent": 24}, {"command": 2, "file": 8, "line": 53, "parent": 48}, {"command": 3, "file": 8, "line": 84, "parent": 24}, {"command": 2, "file": 8, "line": 53, "parent": 50}, {"command": 3, "file": 8, "line": 84, "parent": 24}, {"command": 2, "file": 8, "line": 53, "parent": 52}, {"command": 7, "file": 2, "line": 54, "parent": 7}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 11, "parent": 55}, {"command": 7, "file": 11, "line": 30, "parent": 56}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 12, "parent": 58}, {"command": 7, "file": 12, "line": 23, "parent": 59}, {"command": 7, "file": 6, "line": 40, "parent": 9}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 54, "path": "D:/newfuxios/tool/calbuild/include"}, {"backtrace": 57, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 60, "path": "C:/opt/opencv/build/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "C:/opt/glog/include"}, {"backtrace": 61, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}], "id": "toolcalbuild::@a167bea24520843f7e43", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicommon\\MinSizeRel\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 21, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 45, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 49, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 53, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "toolcalbuild", "nameOnDisk": "toolcalbuild.exe", "paths": {"build": "tool/calbuild", "source": "tool/calbuild"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "tool/calbuild/src/create_board.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}