#include "MoistureAnalyzerController.h"
#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "glog.h"
#include "TaskHelper.h"

MoistureAnalyzerController::MoistureAnalyzerController(
    std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> driver)
    : m_driver(driver)
{

    // 初始化最后的水分测定结果为无效状态
    m_lastMoistureResult.success = false;
    m_lastMoistureResult.initialWeight = 0.0;
    m_lastMoistureResult.dryWeight = 0.0;
    m_lastMoistureResult.moisture = 0.0;
    m_lastMoistureResult.errorMsg = "No measurement performed yet";

    LOG(INFO) << "Moisture analyzer controller created";
}

MoistureAnalyzerController::~MoistureAnalyzerController() {
    LOG(INFO) << "Moisture analyzer controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus MoistureAnalyzerController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Moisture Analyzer";
    status.description = "Intelligent moisture analyzer";
    status.updateTime = TaskHelper::getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "FAILED";
        status.message = "Driver not initialized";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "FAILED";
        status.message = "Device not connected";
        return status;
    }

    status.status = "SUCCESS";
    status.message = "Device status normal";
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        int taskId = TaskHelper::extractTaskId(request, 0);
        return TaskHelper::adaptTaskResponse(createErrorTask("UNKNOWN", "Missing action parameter", taskId));
    }

    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;

    // 提取taskId（支持字符串和数字类型）
    int taskId = TaskHelper::extractTaskId(request, 0);

    if (action == "ZERO") {
        return TaskHelper::adaptTaskResponse(handleZeroOperation(taskId));
    } else if (action == "BARE") {
        return TaskHelper::adaptTaskResponse(handleBareOperation(taskId));
    } else if (action == "OPEN") {
        return TaskHelper::adaptTaskResponse(handleOpenOperation(taskId));
    } else if (action == "CLOSE") {
        return TaskHelper::adaptTaskResponse(handleCloseOperation(taskId));
    } else if (action == "HEAT") {
        nlohmann::json data = request.contains("data") ? request["data"] : nlohmann::json();
        return TaskHelper::adaptTaskResponse(handleHeatOperation(data, taskId));
    } else if (action == "WEIGHT") {
        return TaskHelper::adaptTaskResponse(handleWeightOperation(taskId));
    } else {
        return TaskHelper::adaptTaskResponse(createErrorTask(action, "Unsupported operation: " + action, taskId));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::queryTask(int taskId) {
    // 先清理旧任务
    cleanupOldTasks();

    // 查询指定taskId的任务状态
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 任务存在，返回当前状态（后台线程会自动更新状态）
        return TaskHelper::adaptTaskResponse(it->second);
    }

    // 任务不存在，返回新格式的错误响应
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = TaskHelper::getCurrentTimeString();

    return TaskHelper::adaptTaskResponse(task);
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleZeroOperation(int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("ZERO", "Device not connected", taskId);
    }

    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("ZERO", nlohmann::json(), taskId);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Zero operation completed";
            return task;
        } else {
            return createErrorTask("ZERO", "Zero operation failed: " + m_driver->getLastError(), taskId);
        }

    } catch (const std::exception& e) {
        return createErrorTask("ZERO", "Zero exception: " + std::string(e.what()), taskId);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleBareOperation(int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("BARE", "Device not connected", taskId);
    }

    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("BARE", nlohmann::json(), taskId);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Bare operation completed";
            return task;
        } else {
            return createErrorTask("BARE", "Tare operation failed: " + m_driver->getLastError(), taskId);
        }

    } catch (const std::exception& e) {
        return createErrorTask("BARE", "Tare exception: " + std::string(e.what()), taskId);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleOpenOperation(int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("OPEN", "Device not connected", taskId);
    }

    try {
        if (m_driver->openChamber()) {
            auto task = createSuccessTask("OPEN", nlohmann::json(), taskId);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Open operation completed";
            return task;
        } else {
            return createErrorTask("OPEN", "Open cover operation failed: " + m_driver->getLastError(), taskId);
        }

    } catch (const std::exception& e) {
        return createErrorTask("OPEN", "Open cover exception: " + std::string(e.what()), taskId);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleCloseOperation(int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("CLOSE", "Device not connected", taskId);
    }

    try {
        if (m_driver->closeChamber()) {
            auto task = createSuccessTask("CLOSE", nlohmann::json(), taskId);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Close operation completed";
            return task;
        } else {
            return createErrorTask("CLOSE", "Close cover operation failed: " + m_driver->getLastError(), taskId);
        }

    } catch (const std::exception& e) {
        return createErrorTask("CLOSE", "Close cover exception: " + std::string(e.what()), taskId);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleHeatOperation(const nlohmann::json& data, int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("HEAT", "Device not connected", taskId);
    }

    try {
        // 解析温度参数，默认230°C
        int targetTemperature = 230;
        if (data.contains("temperature") && data["temperature"].is_number()) {
            targetTemperature = data["temperature"].get<int>();
        }

        // 解析时间参数，默认30分钟
        int maxTimeoutMinutes = 30;
        if (data.contains("timeout") && data["timeout"].is_number()) {
            maxTimeoutMinutes = data["timeout"].get<int>();
        }

        LOG(INFO) << "Heat operation parameters - Temperature: " << targetTemperature
                  << "°C, Max time: " << maxTimeoutMinutes << " minutes";

        // 1. 设置目标温度
        if (!m_driver->setTargetTemperature(targetTemperature)) {
            return createErrorTask("HEAT", "Failed to set target temperature: " + m_driver->getLastError(), taskId);
        }
        LOG(INFO) << "Target temperature set to " << targetTemperature << "°C";

        // 2. 读取当前重量并设置为初始重量
        auto weightReading = m_driver->readNetWeight();
        if (!weightReading.success) {
            return createErrorTask("HEAT", "Failed to read initial weight: " + weightReading.errorMsg, taskId);
        }
        m_driver->setInitialWeight(weightReading.weight);
        LOG(INFO) << "Initial weight set to: " << weightReading.weight << "g";

        // 3. 开始加热
        if (!m_driver->startHeating()) {
            return createErrorTask("HEAT", "Failed to start heating: " + m_driver->getLastError(), taskId);
        }
        LOG(INFO) << "Heating started";

        // 4. 启动异步加热监控
        auto task = createSubmittedTask("HEAT", taskId);
        nlohmann::json responseData;
        responseData["initial_weight"] = weightReading.weight;  // 只保存初始重量，单位g
        task.data = responseData;

        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        // 启动异步监控线程
        std::thread([this, taskId, targetTemperature, maxTimeoutMinutes]() {
            try {
                LOG(INFO) << "Starting async heating monitoring for task " << taskId;

                // 等待设备稳定
                std::this_thread::sleep_for(std::chrono::seconds(10));

                // 执行自动监控（30次不变，容差0.01%，间隔2秒）
                auto progressCallback = [this, taskId](double moisture, int stableCount, int elapsedSeconds) {
                    LOG(INFO) << "Task " << taskId << " - Auto monitor progress - Time: " << elapsedSeconds
                             << "s, Moisture: " << moisture
                             << "%, Stable count: " << stableCount << "/60";
                };

                auto result = m_driver->autoMonitorMoisture(60, 0.01, 2, maxTimeoutMinutes, progressCallback);

                // 停止加热
                if (!m_driver->stopHeating()) {
                    LOG(WARNING) << "Failed to stop heating: " << m_driver->getLastError();
                } else {
                    LOG(INFO) << "Heating stopped for task " << taskId;
                }

                // 存储结果
                {
                    std::lock_guard<std::mutex> lock(m_resultMutex);
                    m_lastMoistureResult = result;
                }

                // 更新任务状态
                {
                    std::lock_guard<std::mutex> lock(m_tasksMutex);
                    auto it = m_tasks.find(taskId);
                    if (it != m_tasks.end()) {
                        if (result.success) {
                            it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                            it->second.message = "Heating and moisture monitoring completed successfully";

                            nlohmann::json completedData;
                            completedData["initial_weight"] = result.initialWeight;  // 数值，单位g
                            completedData["final_weight"] = result.dryWeight;        // 数值，单位g
                            completedData["moisture"] = result.moisture;             // 数值，单位%
                            it->second.data = completedData;

                            LOG(INFO) << "Task " << taskId << " completed successfully - Moisture: " << result.moisture << "%";
                        } else {
                            it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                            it->second.message = "Auto monitoring failed: " + result.errorMsg;
                            LOG(ERROR) << "Task " << taskId << " failed: " << result.errorMsg;
                        }
                        it->second.updateTime = TaskHelper::getCurrentTimeString();
                    }
                }
            } catch (const std::exception& e) {
                LOG(ERROR) << "Exception in async heating monitoring for task " << taskId << ": " << e.what();

                // 更新任务为失败状态
                {
                    std::lock_guard<std::mutex> lock(m_tasksMutex);
                    auto it = m_tasks.find(taskId);
                    if (it != m_tasks.end()) {
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "Heating exception: " + std::string(e.what());
                        it->second.updateTime = TaskHelper::getCurrentTimeString();
                    }
                }
            }
        }).detach(); // 分离线程，让它在后台运行

        LOG(INFO) << "Heat operation submitted successfully for task " << taskId;
        return task;

    } catch (const std::exception& e) {
        return createErrorTask("HEAT", "Heating exception: " + std::string(e.what()), taskId);
    }
}



AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleWeightOperation(int taskId) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("WEIGHT", "Device not connected", taskId);
    }

    try {
        auto weightReading = m_driver->readNetWeight();
        if (weightReading.success) {
            nlohmann::json responseData;
            responseData["weight"] = std::to_string(weightReading.weight);

            auto task = createSuccessTask("WEIGHT", responseData, taskId);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Weight reading completed: " << weightReading.weight << "g";
            return task;
        } else {
            return createErrorTask("WEIGHT", "Failed to read weight: " + weightReading.errorMsg, taskId);
        }

    } catch (const std::exception& e) {
        return createErrorTask("WEIGHT", "Weight reading exception: " + std::string(e.what()), taskId);
    }
}



AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createSuccessTask(const std::string& action, const nlohmann::json& data, int taskId) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Command executed successfully";
    task.data = data;
    task.updateTime = TaskHelper::getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createSubmittedTask(const std::string& action, int taskId) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = TaskHelper::getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createErrorTask(const std::string& action, const std::string& error, int taskId) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = TaskHelper::getCurrentTimeString();

    return task;
}

void MoistureAnalyzerController::cleanupOldTasks() {
    TaskHelper::cleanupOldTasks(m_tasks, m_tasksMutex, 1, "MoistureAnalyzerController");
}




