﻿#ifndef AXIS_DRIVER_H
#define AXIS_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <atomic>
#include <mutex>
#include <thread>
#include <vector>
#include <map>

namespace AnalysisRobot {
namespace Axis {

/**
 * @brief 轴状态枚举
 */
enum class AxisStatus {
    DISCONNECTED = 0,   // 未连接
    IDLE = 1,          // 空闲
    MOVING = 2,        // 运动中
    HOMING = 3,        // 回零中
    FAULT = 4,         // 错误
    EMERGENCY = 5      // 急停
};

/**
 * @brief 轴错误类型
 */
enum class AxisError {
    NONE = 0,              // 无错误
    COMMUNICATION = 1,     // 通信错误
    SERVO_ALARM = 2,       // 伺服报警
    POSITIVE_LIMIT = 3,    // 正限位
    NEGATIVE_LIMIT = 4,    // 负限位
    FOLLOW_ERROR = 5,      // 跟随误差
    HOME_FAILED = 6,       // 回零失败
    MOTION_TIMEOUT = 7     // 运动超时
};

/**
 * @brief 运动参数结构
 */
struct MotionParams {
    double velocity;       // 速度 (脉冲/毫秒)
    double acceleration;   // 加速度 (脉冲/毫秒²)
    double deceleration;   // 减速度 (脉冲/毫秒²)
    double startVelocity;  // 起始速度 (脉冲/毫秒)
    int smoothTime;        // 平滑时间 (毫秒)
    
    MotionParams() 
        : velocity(10.0), acceleration(0.5), deceleration(0.5)
        , startVelocity(0.0), smoothTime(0) {}
};

/**
 * @brief 回零参数结构
 */
struct HomeParams {
    int homeMode;          // 回零模式 (1=HOME回原点, 2=HOME+Index, 3=Index)
    int homeDirection;     // 回零方向 (0=负向, 1=正向)
    long offset;           // 回零偏移 (脉冲)
    double rapidVelocity;  // 快移速度 (脉冲/毫秒)
    double locateVelocity; // 定位速度 (脉冲/毫秒)
    double indexVelocity;  // Index速度 (脉冲/毫秒)
    double acceleration;   // 加速度 (脉冲/毫秒²)
    
    HomeParams()
        : homeMode(1), homeDirection(0), offset(0)
        , rapidVelocity(5.0), locateVelocity(1.0), indexVelocity(0.5)
        , acceleration(0.5) {}
};

/**
 * @brief 四轴机器人配置结构
 */
struct FourAxisRobotConfig {
    std::string cardIP;        // 控制卡IP地址
    std::string pcIP;          // PC端IP地址
    int cardPort;              // 控制卡端口
    int pcPort;                // PC端端口
    int cardNumber;            // 卡号

    // 四轴脉冲限制（控制器原生限制）
    struct PulseLimits {
        long j1_min, j1_max;  // J1轴脉冲限制
        long j2_min, j2_max;  // J2轴脉冲限制
        long j3_min, j3_max;  // J3轴脉冲限制
        long j4_min, j4_max;  // J4轴脉冲限制

        PulseLimits()
            : j1_min(0), j1_max(3000)           // J1脉冲限制 (对应0mm到+300mm)
            , j2_min(-2000), j2_max(0)          // J2脉冲限制 (对应-200mm到0mm)
            , j3_min(-180000), j3_max(0)        // J3脉冲限制 (对应-180°到0°)
            , j4_min(-360000), j4_max(360000)   // J4脉冲限制 (对应-360°到+360°)
        {}
    } pulseLimits;

    FourAxisRobotConfig()
        : cardIP("***********"), pcIP("*************")
        , cardPort(60000), pcPort(60000), cardNumber(1) {}
};

/**
 * @brief 四轴脉冲位置结构（控制器原生单位）
 */
struct JointPulses {
    long j1_pulses;  // J1轴脉冲值
    long j2_pulses;  // J2轴脉冲值
    long j3_pulses;  // J3轴脉冲值
    long j4_pulses;  // J4轴脉冲值

    JointPulses(long j1 = 0, long j2 = 0, long j3 = 0, long j4 = 0)
        : j1_pulses(j1), j2_pulses(j2), j3_pulses(j3), j4_pulses(j4) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(int axisId, AxisStatus status, const std::string& message)>;
using ErrorCallback = std::function<void(int axisId, AxisError error, const std::string& message)>;

/**
 * @brief 四轴机器人运动控制驱动类
 *
 * 基于博派科技EthCAT运动控制卡实现四轴机器人控制
 * 四轴定义：
 * - J1: 上下Z方向移动（-0.075 to 0.07m）
 * - J2: 前后Y方向移动（-0.1 to 0.1m）
 * - J3: 倾倒旋转（-90°完全倾倒，0°不倾倒）
 * - J4: 料瓶旋转（控制转速）
 *
 * 主要功能：
 * - 四轴零点回归
 * - 点位控制
 * - 位置获取
 * - 状态检测
 * - 异常处理
 * - 急停控制
 *
 * 注意：TCP位置保持算法已分离到独立的算法模块中
 */
class AxisDriver {
public:
    /**
     * @brief 构造函数
     */
    AxisDriver();
    
    /**
     * @brief 析构函数
     */
    ~AxisDriver();
    
    // ========== 初始化和连接 ==========
    
    /**
     * @brief 初始化四轴机器人驱动
     * @param config 四轴机器人配置参数
     * @return 是否成功
     */
    bool initialize(const FourAxisRobotConfig& config);
    
    /**
     * @brief 连接控制卡
     * @return 是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    // ========== 轴控制 ==========
    
    /**
     * @brief 使能轴
     * @param axisId 轴号 (1-4)
     * @return 是否成功
     */
    bool enableAxis(int axisId);

    /**
     * @brief 禁用轴
     * @param axisId 轴号 (1-4)
     * @return 是否成功
     */
    bool disableAxis(int axisId);

    /**
     * @brief 检查轴是否使能
     * @param axisId 轴号 (1-4)
     * @return 是否使能
     */
    bool isAxisEnabled(int axisId);

    // ========== 四轴机器人控制（纯脉冲API） ==========

    /**
     * @brief 移动到指定脉冲位置
     * @param pulses 脉冲位置
     * @return 是否成功
     */
    bool moveToPosition(const JointPulses& pulses);

    /**
     * @brief 移动到指定脉冲位置（分别指定）
     * @param j1_pulse J1轴脉冲值
     * @param j2_pulse J2轴脉冲值
     * @param j3_pulse J3轴脉冲值
     * @param j4_pulse J4轴脉冲值
     * @return 是否成功
     */
    bool moveToPosition(long j1_pulse, long j2_pulse, long j3_pulse, long j4_pulse);

    /**
     * @brief 获取当前脉冲位置
     * @return 当前脉冲位置
     */
    JointPulses getCurrentPosition();

    /**
     * @brief 获取当前脉冲位置（分别获取）
     * @param j1_pulse J1轴脉冲值输出
     * @param j2_pulse J2轴脉冲值输出
     * @param j3_pulse J3轴脉冲值输出
     * @param j4_pulse J4轴脉冲值输出
     * @return 是否成功
     */
    bool getCurrentPosition(long& j1_pulse, long& j2_pulse, long& j3_pulse, long& j4_pulse);

    /**
     * @brief 验证脉冲位置是否在限制范围内
     * @param pulses 脉冲位置
     * @return 是否在范围内
     */
    bool validatePulseLimits(const JointPulses& pulses);
    
    // ========== 零点回归 ==========
    
    /**
     * @brief 设置回零参数
     * @param axisId 轴号 (1-3)
     * @param params 回零参数
     * @return 是否成功
     */
    bool setHomeParams(int axisId, const HomeParams& params);
    
    /**
     * @brief 开始回零
     * @param axisId 轴号 (1-3)
     * @return 是否成功
     */
    bool startHoming(int axisId);
    
    /**
     * @brief 停止回零
     * @param axisId 轴号 (1-3)
     * @return 是否成功
     */
    bool stopHoming(int axisId);
    
    /**
     * @brief 检查是否正在回零
     * @param axisId 轴号 (1-3)
     * @return 是否正在回零
     */
    bool isHoming(int axisId);
    
    /**
     * @brief 检查回零是否成功
     * @param axisId 轴号 (1-3)
     * @return 是否回零成功
     */
    bool isHomeSuccess(int axisId);
    
    // ========== 点位控制 ==========
    
    /**
     * @brief 设置运动参数
     * @param axisId 轴号 (1-3)
     * @param params 运动参数
     * @return 是否成功
     */
    bool setMotionParams(int axisId, const MotionParams& params);
    
    /**
     * @brief 绝对位置运动
     * @param axisId 轴号 (1-3)
     * @param position 目标位置 (脉冲)
     * @return 是否成功
     */
    bool moveAbsolute(int axisId, long position);
    
    /**
     * @brief 相对位置运动
     * @param axisId 轴号 (1-3)
     * @param distance 移动距离 (脉冲)
     * @return 是否成功
     */
    bool moveRelative(int axisId, long distance);
    
    /**
     * @brief 停止运动
     * @param axisId 轴号 (1-3)
     * @param emergency 是否急停
     * @return 是否成功
     */
    bool stopMotion(int axisId, bool emergency = false);
    
    /**
     * @brief 停止所有轴
     * @param emergency 是否急停
     * @return 是否成功
     */
    bool stopAllAxes(bool emergency = false);
    
    // ========== 位置获取 ==========
    
    /**
     * @brief 获取当前规划位置
     * @param axisId 轴号 (1-3)
     * @return 规划位置 (脉冲)
     */
    long getCurrentPosition(int axisId);
    
    /**
     * @brief 获取编码器位置
     * @param axisId 轴号 (1-3)
     * @return 编码器位置 (脉冲)
     */
    long getEncoderPosition(int axisId);
    
    /**
     * @brief 获取当前速度
     * @param axisId 轴号 (1-3)
     * @return 当前速度 (脉冲/毫秒)
     */
    double getCurrentVelocity(int axisId);
    
    // ========== 状态检测 ==========
    
    /**
     * @brief 获取轴状态
     * @param axisId 轴号 (1-3)
     * @return 轴状态
     */
    AxisStatus getAxisStatus(int axisId);
    
    /**
     * @brief 检查是否到达目标位置
     * @param axisId 轴号 (1-3)
     * @return 是否到达
     */
    bool isAtTarget(int axisId);
    
    /**
     * @brief 检查是否在运动
     * @param axisId 轴号 (1-3)
     * @return 是否在运动
     */
    bool isMoving(int axisId);
    
    /**
     * @brief 获取轴错误状态
     * @param axisId 轴号 (1-3)
     * @return 错误类型
     */
    AxisError getAxisError(int axisId);
    
    // ========== 异常处理 ==========
    
    /**
     * @brief 清除轴异常
     * @param axisId 轴号 (1-3)
     * @return 是否成功
     */
    bool clearAxisError(int axisId);
    
    /**
     * @brief 清除所有轴异常
     * @return 是否成功
     */
    bool clearAllErrors();
    
    // ========== 急停控制 ==========
    
    /**
     * @brief 急停
     * @return 是否成功
     */
    bool emergencyStop();
    
    /**
     * @brief 复位急停
     * @return 是否成功
     */
    bool resetEmergencyStop();
    
    /**
     * @brief 检查是否处于急停状态
     * @return 是否急停
     */
    bool isEmergencyStop();
    
    // ========== 回调设置 ==========
    
    /**
     * @brief 设置状态回调
     * @param callback 状态回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 设置错误回调
     * @param callback 错误回调函数
     */
    void setErrorCallback(ErrorCallback callback);
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    FourAxisRobotConfig m_config;           // 四轴机器人配置参数
    std::atomic<bool> m_connected;          // 连接状态
    std::atomic<bool> m_emergencyStop;     // 急停状态
    StatusCallback m_statusCallback;        // 状态回调
    ErrorCallback m_errorCallback;          // 错误回调
    std::string m_lastError;               // 最后错误信息
    mutable std::mutex m_mutex;            // 线程安全锁

    // 四轴状态
    std::vector<AxisStatus> m_axisStatus;   // 轴状态数组 (4轴)
    std::vector<AxisError> m_axisError;     // 轴错误数组 (4轴)
    std::vector<long> m_targetPosition;     // 目标脉冲位置数组 (4轴)

    // 四轴机器人状态
    JointPulses m_currentPulses;            // 当前脉冲位置
    
    // 状态监控线程
    std::thread m_monitorThread;
    std::atomic<bool> m_monitorRunning;
    
    /**
     * @brief 验证轴号 (四轴: 1-4)
     * @param axisId 轴号
     * @return 是否有效
     */
    bool validateAxisId(int axisId) const;

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * @brief 验证脉冲位置限制
     * @param pulses 脉冲位置
     * @return 是否在限制范围内
     */
    bool validatePulseLimitsInternal(const JointPulses& pulses) const;

    /**
     * @brief 将脉冲位置限制到安全范围
     * @param pulses 脉冲位置
     * @return 限制后的脉冲位置
     */
    JointPulses clampPulsePosition(const JointPulses& pulses) const;
    
    /**
     * @brief 状态监控线程函数
     */
    void monitorThread();
    
    /**
     * @brief 更新轴状态
     * @param axisId 轴号
     */
    void updateAxisStatus(int axisId);
    
    /**
     * @brief 触发状态回调
     * @param axisId 轴号
     * @param status 状态
     * @param message 消息
     */
    void triggerStatusCallback(int axisId, AxisStatus status, const std::string& message);
    
    /**
     * @brief 触发错误回调
     * @param axisId 轴号
     * @param error 错误
     * @param message 消息
     */
    void triggerErrorCallback(int axisId, AxisError error, const std::string& message);
};

} // namespace Axis
} // namespace AnalysisRobot

#endif // AXIS_DRIVER_H
