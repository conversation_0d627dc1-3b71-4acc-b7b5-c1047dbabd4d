{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 136]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-Debug-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 137]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-Debug-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 85]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-Debug-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [11, 87]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-Debug-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 91]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-Debug-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 88]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-Debug-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 89]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-Debug-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 90]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-Debug-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 93]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-Debug-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 92]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-Debug-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 83]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-Debug-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 84]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-Debug-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 86]}, {"build": "Analysis_Robot/test/balanceDriverTest", "jsonFile": "directory-Analysis_Robot.test.balanceDriverTest-Debug-7e4f5522755cfdc4e01f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/balanceDriverTest", "targetIndexes": [41, 94]}, {"build": "Analysis_Robot/test/balanceTest", "jsonFile": "directory-Analysis_Robot.test.balanceTest-Debug-4741c58a4b5c91f9121e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/balanceTest", "targetIndexes": [44, 95]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-Debug-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [47, 96]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-Debug-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [50, 97]}, {"build": "Analysis_Robot/test/moistureAnalyzerDriverTest", "jsonFile": "directory-Analysis_Robot.test.moistureAnalyzerDriverTest-Debug-6df43ddc17b8d29e6a25.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "Analysis_Robot/test/moistureAnalyzerDriverTest", "targetIndexes": [52, 98]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-Debug-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer/APP", "targetIndexes": [55, 99]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-Debug-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Library", "targetIndexes": [58, 101]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-Debug-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/App", "targetIndexes": [0, 100]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-Debug-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [64, 102]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-Debug-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [67, 103]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-Debug-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [70, 104]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-Debug-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [73, 105]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-Debug-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [76, 106]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-Debug-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [79, 107]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-Debug-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [4, 114]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-Debug-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [13, 108]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-Debug-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [22, 109]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-Debug-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [31, 110]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-Debug-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [40, 111]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-Debug-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [12, 112]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-Debug-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "RoboticLaserMarking/UI", "targetIndexes": [57, 113]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-Debug-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [63, 115]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-Debug-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "fuxicore", "targetIndexes": [72, 138]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-Debug-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [81, 147]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-Debug-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_abb_socket", "targetIndexes": [28, 116]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-Debug-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_config_manager", "targetIndexes": [54, 117]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-Debug-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_csv", "targetIndexes": [78, 118]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-Debug-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_event_listener", "targetIndexes": [69, 119]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-Debug-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_executor", "targetIndexes": [46, 120]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-Debug-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_executor_context", "targetIndexes": [19, 121]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-Debug-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_fa2204n_balance", "targetIndexes": [61, 122]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-Debug-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [37, 123]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-Debug-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_fileutil", "targetIndexes": [10, 124]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-Debug-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_json", "targetIndexes": [75, 125]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-Debug-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_license_manager", "targetIndexes": [66, 126]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-Debug-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_license_ui", "targetIndexes": [60, 127]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-Debug-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_micro_dosing", "targetIndexes": [51, 155]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-Debug-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_network", "targetIndexes": [43, 128]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-Debug-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_serial", "targetIndexes": [34, 129]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-Debug-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_service_container", "targetIndexes": [25, 130]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-Debug-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_socket", "targetIndexes": [16, 131]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-Debug-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_sqlite", "targetIndexes": [7, 132]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-Debug-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "Test/test_taskflow", "targetIndexes": [80, 133]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-Debug-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "Test/test_twoaixsrobot", "targetIndexes": [77, 134]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-Debug-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "Test/test_xml", "targetIndexes": [74, 135]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-Debug-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [71, 139]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-Debug-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/AuboDriver", "targetIndexes": [68, 140]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-Debug-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [65, 141]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-Debug-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [62, 142]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-Debug-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [56, 143]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-Debug-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [53, 144]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-Debug-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/OpcDa", "targetIndexes": [3, 145]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-Debug-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/OpcUa", "targetIndexes": [2, 146]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-Debug-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/socket", "targetIndexes": [82, 153]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-Debug-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [6, 148]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-Debug-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [9, 149]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-Debug-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [49, 150]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-Debug-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "hardwaredriver/modbus", "targetIndexes": [15, 151]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-Debug-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "hardwaredriver/serial", "targetIndexes": [18, 152]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-Debug-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "hardwaredriver/usbcamera", "targetIndexes": [21, 154]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-Debug-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/calbuild", "targetIndexes": [24, 156]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-Debug-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/cameraCalibrator", "targetIndexes": [27, 158]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-Debug-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/caltest", "targetIndexes": [30, 157]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-Debug-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/communication", "targetIndexes": [33, 159]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-Debug-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecal", "targetIndexes": [36, 160]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-Debug-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaltest", "targetIndexes": [39, 161]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-Debug-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [42, 162]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-Debug-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 80, "source": "tool/handeyecaluipath", "targetIndexes": [45, 163]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-Debug-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 81, "source": "tool/handeyecaluipathAuto", "targetIndexes": [48, 164]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-Debug-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 82, "source": "tool/verify_calibration", "targetIndexes": [59, 165]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 136]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 137]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 85]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [11, 87]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 91]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 88]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 89]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 90]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 93]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 92]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 83]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 84]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 86]}, {"directoryIndexes": [13], "name": "Analysis_RobottestbalanceDriverTest", "parentIndex": 0, "targetIndexes": [41, 94]}, {"directoryIndexes": [14], "name": "Analysis_RobottestbalanceTest", "parentIndex": 0, "targetIndexes": [44, 95]}, {"directoryIndexes": [15], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [47, 96]}, {"directoryIndexes": [16], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [50, 97]}, {"directoryIndexes": [17], "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "parentIndex": 0, "targetIndexes": [52, 98]}, {"directoryIndexes": [18], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [55, 99]}, {"directoryIndexes": [19], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [58, 101]}, {"directoryIndexes": [20], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [0, 100]}, {"directoryIndexes": [21], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [64, 102]}, {"directoryIndexes": [22], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [67, 103]}, {"directoryIndexes": [23], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [70, 104]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [73, 105]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [76, 106]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [79, 107]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [4, 114]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [13, 108]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [22, 109]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [31, 110]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [40, 111]}, {"directoryIndexes": [32], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [12, 112]}, {"directoryIndexes": [33], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [57, 113]}, {"directoryIndexes": [34], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [63, 115]}, {"directoryIndexes": [35], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [72, 138]}, {"directoryIndexes": [36], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [81, 147]}, {"directoryIndexes": [37], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [28, 116]}, {"directoryIndexes": [38], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [54, 117]}, {"directoryIndexes": [39], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [78, 118]}, {"directoryIndexes": [40], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [69, 119]}, {"directoryIndexes": [41], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [46, 120]}, {"directoryIndexes": [42], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [19, 121]}, {"directoryIndexes": [43], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [61, 122]}, {"directoryIndexes": [44], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [37, 123]}, {"directoryIndexes": [45], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [10, 124]}, {"directoryIndexes": [46], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [75, 125]}, {"directoryIndexes": [47], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [66, 126]}, {"directoryIndexes": [48], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [60, 127]}, {"directoryIndexes": [49], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [51, 155]}, {"directoryIndexes": [50], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [43, 128]}, {"directoryIndexes": [51], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [34, 129]}, {"directoryIndexes": [52], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [25, 130]}, {"directoryIndexes": [53], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [16, 131]}, {"directoryIndexes": [54], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [7, 132]}, {"directoryIndexes": [55], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [80, 133]}, {"directoryIndexes": [56], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [77, 134]}, {"directoryIndexes": [57], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [74, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [71, 139]}, {"directoryIndexes": [59], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [68, 140]}, {"directoryIndexes": [60], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [65, 141]}, {"directoryIndexes": [61], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [62, 142]}, {"directoryIndexes": [62], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [56, 143]}, {"directoryIndexes": [63], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [53, 144]}, {"directoryIndexes": [64], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [3, 145]}, {"directoryIndexes": [65], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [2, 146]}, {"directoryIndexes": [66], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [82, 153]}, {"directoryIndexes": [67], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [6, 148]}, {"directoryIndexes": [68], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [9, 149]}, {"directoryIndexes": [69], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [49, 150]}, {"directoryIndexes": [70], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [15, 151]}, {"directoryIndexes": [71], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [21, 154]}, {"directoryIndexes": [73], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [24, 156]}, {"directoryIndexes": [74], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [27, 158]}, {"directoryIndexes": [75], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [30, 157]}, {"directoryIndexes": [76], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [33, 159]}, {"directoryIndexes": [77], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [36, 160]}, {"directoryIndexes": [78], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [39, 161]}, {"directoryIndexes": [79], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [42, 162]}, {"directoryIndexes": [80], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [45, 163]}, {"directoryIndexes": [81], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [48, 164]}, {"directoryIndexes": [82], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [59, 165]}], "targets": [{"directoryIndex": 20, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-Debug-85f9776f404a9d04d70a.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-48c30841ee9a4a3d95b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 65, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-Debug-f4b1615a7a5f556f591f.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 64, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-Debug-9a5f96b8c74a723de6cc.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 27, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-Debug-8ed5b5467f5c79f1561f.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-Debug-33c83275e6e017ead8c0.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 67, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-Debug-0963848655f3ab3e2fd2.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 54, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-Debug-285251f63eda448c4207.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-Debug-5a96df7c6ffc74b0fbb3.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 68, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-Debug-5532b6fa9f76d055fb06.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 45, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-Debug-a5231ac37c8cf35ee33f.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-Debug-3c4246728da31ae807d7.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 32, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-Debug-abd4d9488c540bfc51e9.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 28, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-Debug-82d79d319fe701d5945b.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-Debug-f79af737752433390703.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-Debug-f517c18acd3e9be18b13.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 53, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-Debug-8b3dbcab358c45534be6.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-Debug-e7a1b0e529138820a42f.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-Debug-c72ba7438a0ccd6ffa04.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 42, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-Debug-8e4216a5c53dd1a0c6f3.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-Debug-6352b9d286d39318039f.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-Debug-13d256b4d376dcd56476.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 29, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-Debug-a12e7c36da385a540597.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-Debug-015880a41437497ef3b7.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 73, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-Debug-9355cfe3a413a237237d.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 52, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-Debug-9716f0a05fbb83bbb3bd.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-Debug-df3268a84b2aed103915.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 74, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-Debug-dfadcaeeba9ba64bed1e.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 37, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-Debug-a8f6f57345b3841fd693.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-Debug-2730ff5697c12566c597.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 75, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-Debug-15c591b4f06f78385e7a.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 30, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-Debug-2e1ecc39902bf749266f.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-Debug-5baf0da4e402fa264c0b.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-Debug-7e206d9c856c97db078a.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 51, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-Debug-dcc410c1d8b0e69cd111.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-Debug-13b772661bc830f004b9.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-Debug-bf3efcfdc18b56070539.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 44, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-Debug-2e1bb9c077e306455584.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-Debug-03c5be1bc5bd216d3958.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-Debug-ac7845ad0cc7df660bc4.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 31, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-Debug-9ac4daac53e8de4b0373.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 13, "id": "ALL_BUILD::@f2032f6c36bb657d8ab6", "jsonFile": "target-ALL_BUILD-Debug-7a5251c4190b7ebcb7d2.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-Debug-b8867bbeaf0f6148c31f.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 50, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-Debug-6b5ac6e9946f6ca19396.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 14, "id": "ALL_BUILD::@67fcef0db64755a7efa1", "jsonFile": "target-ALL_BUILD-Debug-902e971a81d0d055fcd1.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 80, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-Debug-54f6bda5ac93bf0a16e1.json", "name": "ALL_BUILD", "projectIndex": 80}, {"directoryIndex": 41, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-Debug-179f33c45a88edf1cc65.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 15, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-Debug-8b2129794a5b7213244f.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 81, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-Debug-017bc66adac4d25af138.json", "name": "ALL_BUILD", "projectIndex": 81}, {"directoryIndex": 69, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-Debug-e7dc7b5610988139a6bf.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 16, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-Debug-89a06e46db00817d896d.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 49, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-Debug-25ccdf49d4f252d4663c.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 17, "id": "ALL_BUILD::@344f5fe5f7bc7b70cade", "jsonFile": "target-ALL_BUILD-Debug-b028a33ff2ea2898cc11.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 63, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-Debug-95a0d049fbb3ffaaca04.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 38, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-Debug-76ec84edc0fb36542d20.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 18, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-Debug-4bd9588263a2e6bc50cf.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 62, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-Debug-b1f9058584bf0a918bba.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 33, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-Debug-661f08955d464024345a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 19, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-Debug-39921dc5635a199dc5c3.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 82, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-Debug-51a0fa8c48f3509ec23a.json", "name": "ALL_BUILD", "projectIndex": 82}, {"directoryIndex": 48, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-Debug-24975862660e8c214b60.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 43, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-Debug-7c47365a41fa76d4dea2.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 61, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-Debug-bbe04bcf2e657f51af19.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 34, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-Debug-a6ab1cea57c62f3e1a60.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 21, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-Debug-8e59bdd4027a8bf10ae5.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 60, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-Debug-b74498e8f63e9b1b74ac.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 47, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-Debug-6933b8546b999c09fb7c.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 22, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-Debug-8397406cb0e0cf7992b8.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 59, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-Debug-ef0da601182b6b6b8f17.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 40, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-Debug-3bf19658289dd65e46ef.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 23, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-Debug-cf52e46cdfb4ab946667.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 58, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-Debug-5110c23ae1c198d6bda3.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 35, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-Debug-569802fed0e125d31ab4.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 24, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-Debug-adbed5082fd27d9fb126.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 57, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-Debug-7f01a577f19cd6b318f3.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 46, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-Debug-e6a290df2ee065fd2889.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 25, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-Debug-a521958a0993fcac108a.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 56, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-Debug-d6bc1818314a184f1537.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 39, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-Debug-7ffa9bfdcce8201ba5d2.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 26, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-Debug-e64cb122a0640fbfbfd4.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 55, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-Debug-7e98f33b92a345e5c7b6.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 36, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-Debug-afdcfcd02f0d3fc4b858.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 66, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-Debug-d5422a0ec356725cf693.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-Debug-3f77e46eeef3c5e464e1.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-Debug-bfb97d2de91d2f564b8d.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-Debug-6087cddf137e4c4dfcc0.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-Debug-27ca7ca4fa2a2f1baac2.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-Debug-9d18c88c373eb8f66c98.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-Debug-ef36b39e2a9db51d84e8.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-Debug-b4993330d7f71e57542f.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-Debug-8a5f95f3f70fa07df128.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-Debug-8b6f9e98987b56166f07.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-Debug-0be8f905071f26010cd6.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-Debug-6680cd051d38a55c8019.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestbalanceDriverTest::@f2032f6c36bb657d8ab6", "jsonFile": "target-Analysis_RobottestbalanceDriverTest-Debug-a2410f9bb91cbeff5cb7.json", "name": "Analysis_RobottestbalanceDriverTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestbalanceTest::@67fcef0db64755a7efa1", "jsonFile": "target-Analysis_RobottestbalanceTest-Debug-d9c37fa409cacef8b463.json", "name": "Analysis_RobottestbalanceTest", "projectIndex": 14}, {"directoryIndex": 15, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-Debug-042d308e89822e757f71.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 15}, {"directoryIndex": 16, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-Debug-c0b1412111759eda6a3b.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 16}, {"directoryIndex": 17, "id": "Analysis_RobottestmoistureAnalyzerDriverTest::@344f5fe5f7bc7b70cade", "jsonFile": "target-Analysis_RobottestmoistureAnalyzerDriverTest-Debug-53d52a16f8191e10e257.json", "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "projectIndex": 17}, {"directoryIndex": 18, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-Debug-2655962b8612922f247d.json", "name": "MJServerAPP", "projectIndex": 18}, {"directoryIndex": 20, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-Debug-141c0e832d99d9fd1a13.json", "name": "MJServer_RefactorApp", "projectIndex": 20}, {"directoryIndex": 19, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-Debug-7e296f8389430a24aa93.json", "name": "MJServer_RefactorLibrary", "projectIndex": 19}, {"directoryIndex": 21, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-Debug-d7844828ee1717c2694f.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 21}, {"directoryIndex": 22, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-Debug-568a92eda6fc0efe2b1d.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 22}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-Debug-05d8e33e7d62630f8df1.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 23}, {"directoryIndex": 24, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-Debug-492a70acc87fe9dcf1a5.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 24}, {"directoryIndex": 25, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-Debug-30b9eec7649196a0074a.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-Debug-d8348470391cc5bd4f50.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 26}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-Debug-2a0146694f999fcd912d.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-Debug-1f8a4858511e3c5b5e55.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-Debug-2e64260f8b73a4f4e819.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 30}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-Debug-2c61df730d250244a786.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 31}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-Debug-4b4c373dd0f73fc3a9d5.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 32}, {"directoryIndex": 33, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-Debug-f4441f3a1b6d52738ee2.json", "name": "RoboticLaserMarkingUI", "projectIndex": 33}, {"directoryIndex": 27, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-Debug-7c2f5cc8807dfa20e1e8.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 27}, {"directoryIndex": 34, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-Debug-88d1db417d810ef835be.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 34}, {"directoryIndex": 37, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-Debug-3edd6a800db6c18e971f.json", "name": "Testtest_abb_socket", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-Debug-d6ea8110a663c17adf76.json", "name": "Testtest_config_manager", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-Debug-c007c7c80e8499e01c1a.json", "name": "Testtest_csv", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-Debug-2ecc9e2df8840a47b8a0.json", "name": "Testtest_event_listener", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-Debug-fa8cd2826225a35b1d04.json", "name": "Testtest_executor", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-Debug-289735b307b28a588fb6.json", "name": "Testtest_executor_context", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-Debug-313f3bc9777e7864eed3.json", "name": "Testtest_fa2204n_balance", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-Debug-ac3b0e0d82fca47fad27.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-Debug-6d5573cfe9c9b375955b.json", "name": "Testtest_fileutil", "projectIndex": 45}, {"directoryIndex": 46, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-Debug-e7d0e2edd6cb8c5b1f19.json", "name": "Testtest_json", "projectIndex": 46}, {"directoryIndex": 47, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-Debug-402e74f89c1fa25d0849.json", "name": "Testtest_license_manager", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-Debug-076c12582b5ce7d6286b.json", "name": "Testtest_license_ui", "projectIndex": 48}, {"directoryIndex": 50, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-Debug-e959e646c7e7159a4f06.json", "name": "Testtest_network", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-Debug-7445c733a477c4557507.json", "name": "Testtest_serial", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-Debug-fc5247102b43b9d1e1d9.json", "name": "Testtest_service_container", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-Debug-22fa45f74cf8b81d5dfe.json", "name": "Testtest_socket", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-Debug-9e9928674e74f3f81026.json", "name": "Testtest_sqlite", "projectIndex": 54}, {"directoryIndex": 55, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-Debug-f5a3945611fdd154cb1f.json", "name": "Testtest_taskflow", "projectIndex": 55}, {"directoryIndex": 56, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-Debug-ef310742e3016aeb94de.json", "name": "Testtest_twoaixsrobot", "projectIndex": 56}, {"directoryIndex": 57, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-Debug-79b61d4e174cea861768.json", "name": "Testtest_xml", "projectIndex": 57}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-Debug-49c1fdd919b15d401ce4.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 35, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-Debug-3e93764fc5e0523cbd0a.json", "name": "fuxicore", "projectIndex": 35}, {"directoryIndex": 58, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-Debug-9df51ad70e9a22dbac85.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-Debug-63f691fbb0f54a9efaa6.json", "name": "hardwaredriverAuboDriver", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-Debug-6dac6b56c57ab5dc5014.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-Debug-beeaf38774b650e211da.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-Debug-224b1bac04f83a5b731b.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 62}, {"directoryIndex": 63, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-Debug-83a0aeeaec4f11985109.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 63}, {"directoryIndex": 64, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-Debug-14a68381ab8fd41c2e10.json", "name": "hardwaredriverOpcDa", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-Debug-81692c75f74f10c02425.json", "name": "hardwaredriverOpcUa", "projectIndex": 65}, {"directoryIndex": 36, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-Debug-60f918d5c893ee5cad27.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 36}, {"directoryIndex": 67, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-Debug-6897c40c7ef0671279dc.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-Debug-436a45e8afc6e973499d.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 68}, {"directoryIndex": 69, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-Debug-2bc90bd1b5aff8387267.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 69}, {"directoryIndex": 70, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-Debug-63860956d4b5aa14b140.json", "name": "hardwaredrivermodbus", "projectIndex": 70}, {"directoryIndex": 71, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-Debug-cf1cfdbc455f2f286534.json", "name": "hardwaredriverserial", "projectIndex": 71}, {"directoryIndex": 66, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-Debug-f07047b3e4da01d9d2ab.json", "name": "hardwaredriversocket", "projectIndex": 66}, {"directoryIndex": 72, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-Debug-acb93b2bf6c727c27816.json", "name": "hardwaredriverusbcamera", "projectIndex": 72}, {"directoryIndex": 49, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-Debug-0b25ae45da907f366d52.json", "name": "test_micro_dosing", "projectIndex": 49}, {"directoryIndex": 73, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-Debug-65a0840896faa99bba67.json", "name": "toolcalbuild", "projectIndex": 73}, {"directoryIndex": 75, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-Debug-eee00f2d0e7df05f7e11.json", "name": "toolcaltest", "projectIndex": 75}, {"directoryIndex": 74, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-Debug-4685320d407f41350367.json", "name": "toolcameraCalibrator", "projectIndex": 74}, {"directoryIndex": 76, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-Debug-7f35d35492cb3be40725.json", "name": "toolcommunication", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-Debug-cb8416eb13a6a2ece2f6.json", "name": "toolhandeyecal", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-Debug-9849abac4c757ea0d599.json", "name": "toolhandeyecaltest", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-Debug-5842cbb3f3cbde1c127d.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 79}, {"directoryIndex": 80, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-Debug-d41251cb0a3a65b1af3b.json", "name": "toolhandeyecaluipath", "projectIndex": 80}, {"directoryIndex": 81, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-Debug-38e2d213f76128fd8bf5.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 81}, {"directoryIndex": 82, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-Debug-b35babcbc036e7fcf482.json", "name": "toolverify_calibration", "projectIndex": 82}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 136]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-Release-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 137]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-Release-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 85]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-Release-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [11, 87]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-Release-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 91]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-Release-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 88]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-Release-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 89]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-Release-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 90]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-Release-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 93]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-Release-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 92]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-Release-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 83]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-Release-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 84]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-Release-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 86]}, {"build": "Analysis_Robot/test/balanceDriverTest", "jsonFile": "directory-Analysis_Robot.test.balanceDriverTest-Release-7e4f5522755cfdc4e01f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/balanceDriverTest", "targetIndexes": [41, 94]}, {"build": "Analysis_Robot/test/balanceTest", "jsonFile": "directory-Analysis_Robot.test.balanceTest-Release-4741c58a4b5c91f9121e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/balanceTest", "targetIndexes": [44, 95]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-Release-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [47, 96]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-Release-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [50, 97]}, {"build": "Analysis_Robot/test/moistureAnalyzerDriverTest", "jsonFile": "directory-Analysis_Robot.test.moistureAnalyzerDriverTest-Release-6df43ddc17b8d29e6a25.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "Analysis_Robot/test/moistureAnalyzerDriverTest", "targetIndexes": [52, 98]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-Release-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer/APP", "targetIndexes": [55, 99]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-Release-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Library", "targetIndexes": [58, 101]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-Release-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/App", "targetIndexes": [0, 100]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-Release-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [64, 102]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-Release-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [67, 103]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-Release-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [70, 104]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-Release-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [73, 105]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-Release-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [76, 106]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-Release-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [79, 107]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-Release-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [4, 114]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-Release-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [13, 108]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-Release-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [22, 109]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-Release-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [31, 110]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-Release-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [40, 111]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-Release-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [12, 112]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-Release-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "RoboticLaserMarking/UI", "targetIndexes": [57, 113]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-Release-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [63, 115]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-Release-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "fuxicore", "targetIndexes": [72, 138]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-Release-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [81, 147]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-Release-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_abb_socket", "targetIndexes": [28, 116]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-Release-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_config_manager", "targetIndexes": [54, 117]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-Release-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_csv", "targetIndexes": [78, 118]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-Release-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_event_listener", "targetIndexes": [69, 119]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-Release-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_executor", "targetIndexes": [46, 120]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-Release-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_executor_context", "targetIndexes": [19, 121]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-Release-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_fa2204n_balance", "targetIndexes": [61, 122]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-Release-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [37, 123]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-Release-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_fileutil", "targetIndexes": [10, 124]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-Release-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_json", "targetIndexes": [75, 125]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-Release-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_license_manager", "targetIndexes": [66, 126]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-Release-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_license_ui", "targetIndexes": [60, 127]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-Release-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_micro_dosing", "targetIndexes": [51, 155]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-Release-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_network", "targetIndexes": [43, 128]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-Release-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_serial", "targetIndexes": [34, 129]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-Release-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_service_container", "targetIndexes": [25, 130]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-Release-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_socket", "targetIndexes": [16, 131]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-Release-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_sqlite", "targetIndexes": [7, 132]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-Release-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "Test/test_taskflow", "targetIndexes": [80, 133]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-Release-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "Test/test_twoaixsrobot", "targetIndexes": [77, 134]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-Release-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "Test/test_xml", "targetIndexes": [74, 135]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-Release-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [71, 139]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-Release-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/AuboDriver", "targetIndexes": [68, 140]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-Release-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [65, 141]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-Release-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [62, 142]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-Release-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [56, 143]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-Release-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [53, 144]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-Release-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/OpcDa", "targetIndexes": [3, 145]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-Release-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/OpcUa", "targetIndexes": [2, 146]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-Release-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/socket", "targetIndexes": [82, 153]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-Release-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [6, 148]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-Release-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [9, 149]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-Release-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [49, 150]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-Release-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "hardwaredriver/modbus", "targetIndexes": [15, 151]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-Release-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "hardwaredriver/serial", "targetIndexes": [18, 152]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-Release-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "hardwaredriver/usbcamera", "targetIndexes": [21, 154]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-Release-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/calbuild", "targetIndexes": [24, 156]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-Release-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/cameraCalibrator", "targetIndexes": [27, 158]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-Release-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/caltest", "targetIndexes": [30, 157]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-Release-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/communication", "targetIndexes": [33, 159]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-Release-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecal", "targetIndexes": [36, 160]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-Release-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaltest", "targetIndexes": [39, 161]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-Release-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [42, 162]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-Release-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 80, "source": "tool/handeyecaluipath", "targetIndexes": [45, 163]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-Release-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 81, "source": "tool/handeyecaluipathAuto", "targetIndexes": [48, 164]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-Release-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 82, "source": "tool/verify_calibration", "targetIndexes": [59, 165]}], "name": "Release", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 136]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 137]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 85]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [11, 87]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 91]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 88]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 89]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 90]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 93]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 92]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 83]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 84]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 86]}, {"directoryIndexes": [13], "name": "Analysis_RobottestbalanceDriverTest", "parentIndex": 0, "targetIndexes": [41, 94]}, {"directoryIndexes": [14], "name": "Analysis_RobottestbalanceTest", "parentIndex": 0, "targetIndexes": [44, 95]}, {"directoryIndexes": [15], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [47, 96]}, {"directoryIndexes": [16], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [50, 97]}, {"directoryIndexes": [17], "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "parentIndex": 0, "targetIndexes": [52, 98]}, {"directoryIndexes": [18], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [55, 99]}, {"directoryIndexes": [19], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [58, 101]}, {"directoryIndexes": [20], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [0, 100]}, {"directoryIndexes": [21], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [64, 102]}, {"directoryIndexes": [22], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [67, 103]}, {"directoryIndexes": [23], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [70, 104]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [73, 105]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [76, 106]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [79, 107]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [4, 114]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [13, 108]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [22, 109]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [31, 110]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [40, 111]}, {"directoryIndexes": [32], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [12, 112]}, {"directoryIndexes": [33], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [57, 113]}, {"directoryIndexes": [34], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [63, 115]}, {"directoryIndexes": [35], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [72, 138]}, {"directoryIndexes": [36], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [81, 147]}, {"directoryIndexes": [37], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [28, 116]}, {"directoryIndexes": [38], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [54, 117]}, {"directoryIndexes": [39], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [78, 118]}, {"directoryIndexes": [40], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [69, 119]}, {"directoryIndexes": [41], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [46, 120]}, {"directoryIndexes": [42], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [19, 121]}, {"directoryIndexes": [43], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [61, 122]}, {"directoryIndexes": [44], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [37, 123]}, {"directoryIndexes": [45], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [10, 124]}, {"directoryIndexes": [46], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [75, 125]}, {"directoryIndexes": [47], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [66, 126]}, {"directoryIndexes": [48], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [60, 127]}, {"directoryIndexes": [49], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [51, 155]}, {"directoryIndexes": [50], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [43, 128]}, {"directoryIndexes": [51], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [34, 129]}, {"directoryIndexes": [52], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [25, 130]}, {"directoryIndexes": [53], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [16, 131]}, {"directoryIndexes": [54], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [7, 132]}, {"directoryIndexes": [55], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [80, 133]}, {"directoryIndexes": [56], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [77, 134]}, {"directoryIndexes": [57], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [74, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [71, 139]}, {"directoryIndexes": [59], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [68, 140]}, {"directoryIndexes": [60], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [65, 141]}, {"directoryIndexes": [61], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [62, 142]}, {"directoryIndexes": [62], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [56, 143]}, {"directoryIndexes": [63], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [53, 144]}, {"directoryIndexes": [64], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [3, 145]}, {"directoryIndexes": [65], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [2, 146]}, {"directoryIndexes": [66], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [82, 153]}, {"directoryIndexes": [67], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [6, 148]}, {"directoryIndexes": [68], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [9, 149]}, {"directoryIndexes": [69], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [49, 150]}, {"directoryIndexes": [70], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [15, 151]}, {"directoryIndexes": [71], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [21, 154]}, {"directoryIndexes": [73], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [24, 156]}, {"directoryIndexes": [74], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [27, 158]}, {"directoryIndexes": [75], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [30, 157]}, {"directoryIndexes": [76], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [33, 159]}, {"directoryIndexes": [77], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [36, 160]}, {"directoryIndexes": [78], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [39, 161]}, {"directoryIndexes": [79], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [42, 162]}, {"directoryIndexes": [80], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [45, 163]}, {"directoryIndexes": [81], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [48, 164]}, {"directoryIndexes": [82], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [59, 165]}], "targets": [{"directoryIndex": 20, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-Release-85f9776f404a9d04d70a.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-48c30841ee9a4a3d95b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 65, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-Release-f4b1615a7a5f556f591f.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 64, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-Release-9a5f96b8c74a723de6cc.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 27, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-Release-8ed5b5467f5c79f1561f.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-Release-33c83275e6e017ead8c0.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 67, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-Release-0963848655f3ab3e2fd2.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 54, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-Release-285251f63eda448c4207.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-Release-5a96df7c6ffc74b0fbb3.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 68, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-Release-5532b6fa9f76d055fb06.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 45, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-Release-a5231ac37c8cf35ee33f.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-Release-3c4246728da31ae807d7.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 32, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-Release-abd4d9488c540bfc51e9.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 28, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-Release-82d79d319fe701d5945b.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-Release-f79af737752433390703.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-Release-f517c18acd3e9be18b13.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 53, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-Release-8b3dbcab358c45534be6.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-Release-e7a1b0e529138820a42f.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-Release-c72ba7438a0ccd6ffa04.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 42, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-Release-8e4216a5c53dd1a0c6f3.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-Release-6352b9d286d39318039f.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-Release-13d256b4d376dcd56476.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 29, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-Release-a12e7c36da385a540597.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-Release-015880a41437497ef3b7.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 73, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-Release-9355cfe3a413a237237d.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 52, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-Release-9716f0a05fbb83bbb3bd.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-Release-df3268a84b2aed103915.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 74, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-Release-dfadcaeeba9ba64bed1e.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 37, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-Release-a8f6f57345b3841fd693.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-Release-2730ff5697c12566c597.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 75, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-Release-15c591b4f06f78385e7a.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 30, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-Release-2e1ecc39902bf749266f.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-Release-5baf0da4e402fa264c0b.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-Release-7e206d9c856c97db078a.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 51, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-Release-dcc410c1d8b0e69cd111.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-Release-13b772661bc830f004b9.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-Release-bf3efcfdc18b56070539.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 44, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-Release-2e1bb9c077e306455584.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-Release-03c5be1bc5bd216d3958.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-Release-ac7845ad0cc7df660bc4.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 31, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-Release-9ac4daac53e8de4b0373.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 13, "id": "ALL_BUILD::@f2032f6c36bb657d8ab6", "jsonFile": "target-ALL_BUILD-Release-7a5251c4190b7ebcb7d2.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-Release-b8867bbeaf0f6148c31f.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 50, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-Release-6b5ac6e9946f6ca19396.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 14, "id": "ALL_BUILD::@67fcef0db64755a7efa1", "jsonFile": "target-ALL_BUILD-Release-902e971a81d0d055fcd1.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 80, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-Release-54f6bda5ac93bf0a16e1.json", "name": "ALL_BUILD", "projectIndex": 80}, {"directoryIndex": 41, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-Release-179f33c45a88edf1cc65.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 15, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-Release-8b2129794a5b7213244f.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 81, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-Release-017bc66adac4d25af138.json", "name": "ALL_BUILD", "projectIndex": 81}, {"directoryIndex": 69, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-Release-e7dc7b5610988139a6bf.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 16, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-Release-89a06e46db00817d896d.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 49, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-Release-25ccdf49d4f252d4663c.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 17, "id": "ALL_BUILD::@344f5fe5f7bc7b70cade", "jsonFile": "target-ALL_BUILD-Release-b028a33ff2ea2898cc11.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 63, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-Release-95a0d049fbb3ffaaca04.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 38, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-Release-76ec84edc0fb36542d20.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 18, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-Release-4bd9588263a2e6bc50cf.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 62, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-Release-b1f9058584bf0a918bba.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 33, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-Release-661f08955d464024345a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 19, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-Release-39921dc5635a199dc5c3.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 82, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-Release-51a0fa8c48f3509ec23a.json", "name": "ALL_BUILD", "projectIndex": 82}, {"directoryIndex": 48, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-Release-24975862660e8c214b60.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 43, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-Release-7c47365a41fa76d4dea2.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 61, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-Release-bbe04bcf2e657f51af19.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 34, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-Release-a6ab1cea57c62f3e1a60.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 21, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-Release-8e59bdd4027a8bf10ae5.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 60, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-Release-b74498e8f63e9b1b74ac.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 47, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-Release-6933b8546b999c09fb7c.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 22, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-Release-8397406cb0e0cf7992b8.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 59, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-Release-ef0da601182b6b6b8f17.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 40, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-Release-3bf19658289dd65e46ef.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 23, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-Release-cf52e46cdfb4ab946667.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 58, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-Release-5110c23ae1c198d6bda3.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 35, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-Release-569802fed0e125d31ab4.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 24, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-Release-adbed5082fd27d9fb126.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 57, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-Release-7f01a577f19cd6b318f3.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 46, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-Release-e6a290df2ee065fd2889.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 25, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-Release-a521958a0993fcac108a.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 56, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-Release-d6bc1818314a184f1537.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 39, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-Release-7ffa9bfdcce8201ba5d2.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 26, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-Release-e64cb122a0640fbfbfd4.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 55, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-Release-7e98f33b92a345e5c7b6.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 36, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-Release-afdcfcd02f0d3fc4b858.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 66, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-Release-d5422a0ec356725cf693.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-Release-8c61e4c979f5df86064f.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-Release-79d2379987188be5dd50.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-Release-1f16687dbc871b2f802f.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-Release-bae3eb94e873f5b81b56.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-Release-adc1d1ab1c219eb9d45c.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-Release-dec8d3458088764000e4.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-Release-b567d4ed2ce762032a7f.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-Release-70a7ea8a9808ee135369.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-Release-f296dca23f804c342430.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-Release-bfabd23683addf0a9a90.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-Release-4e600f6440696d671ce0.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestbalanceDriverTest::@f2032f6c36bb657d8ab6", "jsonFile": "target-Analysis_RobottestbalanceDriverTest-Release-3f45a47705b1863f9d8c.json", "name": "Analysis_RobottestbalanceDriverTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestbalanceTest::@67fcef0db64755a7efa1", "jsonFile": "target-Analysis_RobottestbalanceTest-Release-7cd087816c5b6e8b5a60.json", "name": "Analysis_RobottestbalanceTest", "projectIndex": 14}, {"directoryIndex": 15, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-Release-025dd57c380a3e18910f.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 15}, {"directoryIndex": 16, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-Release-087ca8dc8b92350782ad.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 16}, {"directoryIndex": 17, "id": "Analysis_RobottestmoistureAnalyzerDriverTest::@344f5fe5f7bc7b70cade", "jsonFile": "target-Analysis_RobottestmoistureAnalyzerDriverTest-Release-47d4f46df1a9540d55fc.json", "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "projectIndex": 17}, {"directoryIndex": 18, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-Release-8187fe6f2b97630eed16.json", "name": "MJServerAPP", "projectIndex": 18}, {"directoryIndex": 20, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-Release-c3559942787ee8e4c24e.json", "name": "MJServer_RefactorApp", "projectIndex": 20}, {"directoryIndex": 19, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-Release-c55175f2e17520701d1e.json", "name": "MJServer_RefactorLibrary", "projectIndex": 19}, {"directoryIndex": 21, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-Release-e92ccba8abae17e3a3e0.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 21}, {"directoryIndex": 22, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-Release-0647fd2da2b6a3321abe.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 22}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-Release-20d11d70eba14aeda4aa.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 23}, {"directoryIndex": 24, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-Release-c97adb34fb3b5c1cfb7b.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 24}, {"directoryIndex": 25, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-Release-2ab440867dc1bfd73696.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-Release-c8d605402868f664c2a4.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 26}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-Release-4f831be65e6e8b27e06c.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-Release-947bc010483fae166e3f.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-Release-b0ec19c0db5823aad96b.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 30}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-Release-1987e60de78a1022492e.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 31}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-Release-579cab2e6ffc79ccc638.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 32}, {"directoryIndex": 33, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-Release-51300c7380a0feac9f40.json", "name": "RoboticLaserMarkingUI", "projectIndex": 33}, {"directoryIndex": 27, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-Release-5e6bef6f8fd932300f7b.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 27}, {"directoryIndex": 34, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-Release-929c0e489d5f98d6b04f.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 34}, {"directoryIndex": 37, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-Release-711762281a438f5318e0.json", "name": "Testtest_abb_socket", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-Release-e0e0e7fb08672784d07e.json", "name": "Testtest_config_manager", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-Release-d6a82831875300372672.json", "name": "Testtest_csv", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-Release-f4f888be4f06f71bb5c7.json", "name": "Testtest_event_listener", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-Release-53153f359e0000a53183.json", "name": "Testtest_executor", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-Release-7317449821e72ada82b5.json", "name": "Testtest_executor_context", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-Release-535018814ca84cf489ec.json", "name": "Testtest_fa2204n_balance", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-Release-0f64947e5ca9acaf5726.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-Release-42d1a2ca29d0a1e6625a.json", "name": "Testtest_fileutil", "projectIndex": 45}, {"directoryIndex": 46, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-Release-3cf7bda8cdda7ec2b255.json", "name": "Testtest_json", "projectIndex": 46}, {"directoryIndex": 47, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-Release-5e42d1db7d058443ac73.json", "name": "Testtest_license_manager", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-Release-e7d2c33acc1118eeb1ed.json", "name": "Testtest_license_ui", "projectIndex": 48}, {"directoryIndex": 50, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-Release-f2e5fc5eef4262949d45.json", "name": "Testtest_network", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-Release-9ebb215fba88ec125b03.json", "name": "Testtest_serial", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-Release-87a4b2f70312aa1740d2.json", "name": "Testtest_service_container", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-Release-159080fb655dbb9ff1a9.json", "name": "Testtest_socket", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-Release-d1ae8e01408e482b9b27.json", "name": "Testtest_sqlite", "projectIndex": 54}, {"directoryIndex": 55, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-Release-d4e39b8dacfa105a886b.json", "name": "Testtest_taskflow", "projectIndex": 55}, {"directoryIndex": 56, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-Release-49763a95e5979f2ee208.json", "name": "Testtest_twoaixsrobot", "projectIndex": 56}, {"directoryIndex": 57, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-Release-c8689dba5779a2fc534f.json", "name": "Testtest_xml", "projectIndex": 57}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-Release-893c2a8474cd017989f8.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 35, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-Release-146f061111b9fc14df45.json", "name": "fuxicore", "projectIndex": 35}, {"directoryIndex": 58, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-Release-2de5a1f3e88dcad5f06c.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-Release-966c8a01fdd89bdd8bb4.json", "name": "hardwaredriverAuboDriver", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-Release-e436aee86e9e3826c2a5.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-Release-199a563cb206e3cb85e9.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-Release-430789c63e76c71bb49a.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 62}, {"directoryIndex": 63, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-Release-4a316414ab97b9b7b25e.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 63}, {"directoryIndex": 64, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-Release-066fd6939ab2a07a2796.json", "name": "hardwaredriverOpcDa", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-Release-69f5ff09da20791d1f7d.json", "name": "hardwaredriverOpcUa", "projectIndex": 65}, {"directoryIndex": 36, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-Release-0daca241fceb9aa65dcd.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 36}, {"directoryIndex": 67, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-Release-003c7487c398d69b462f.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-Release-07e519f7a8723ed1352a.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 68}, {"directoryIndex": 69, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-Release-f4999c52881e33552678.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 69}, {"directoryIndex": 70, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-Release-1e8067720d9f79b865b3.json", "name": "hardwaredrivermodbus", "projectIndex": 70}, {"directoryIndex": 71, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-Release-d317f9e5bc4dae89a001.json", "name": "hardwaredriverserial", "projectIndex": 71}, {"directoryIndex": 66, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-Release-7e731e85912215b89b7b.json", "name": "hardwaredriversocket", "projectIndex": 66}, {"directoryIndex": 72, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-Release-e598944c5f2c85c56a41.json", "name": "hardwaredriverusbcamera", "projectIndex": 72}, {"directoryIndex": 49, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-Release-7f594a43c09f1bd8e987.json", "name": "test_micro_dosing", "projectIndex": 49}, {"directoryIndex": 73, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-Release-6f53ef046c750ec10efa.json", "name": "toolcalbuild", "projectIndex": 73}, {"directoryIndex": 75, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-Release-0c5f542fbdfd3d9900b7.json", "name": "toolcaltest", "projectIndex": 75}, {"directoryIndex": 74, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-Release-84fdb30d3b026aae96b3.json", "name": "toolcameraCalibrator", "projectIndex": 74}, {"directoryIndex": 76, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-Release-293d1ec0c5b6d5b363e2.json", "name": "toolcommunication", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-Release-a7f8c1c882c961d3c6ad.json", "name": "toolhandeyecal", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-Release-a8c1017408304f88325d.json", "name": "toolhandeyecaltest", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-Release-01f4c015274e7977c0aa.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 79}, {"directoryIndex": 80, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-Release-9b9f292e8521f365cc47.json", "name": "toolhandeyecaluipath", "projectIndex": 80}, {"directoryIndex": 81, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-Release-cf064bd1894a2016b78c.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 81}, {"directoryIndex": 82, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-Release-806b292dcf97f1520ce9.json", "name": "toolverify_calibration", "projectIndex": 82}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 136]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-MinSizeRel-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 137]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-MinSizeRel-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 85]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-MinSizeRel-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [11, 87]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-MinSizeRel-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 91]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-MinSizeRel-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 88]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-MinSizeRel-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 89]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-MinSizeRel-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 90]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-MinSizeRel-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 93]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-MinSizeRel-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 92]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-MinSizeRel-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 83]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-MinSizeRel-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 84]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-MinSizeRel-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 86]}, {"build": "Analysis_Robot/test/balanceDriverTest", "jsonFile": "directory-Analysis_Robot.test.balanceDriverTest-MinSizeRel-7e4f5522755cfdc4e01f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/balanceDriverTest", "targetIndexes": [41, 94]}, {"build": "Analysis_Robot/test/balanceTest", "jsonFile": "directory-Analysis_Robot.test.balanceTest-MinSizeRel-4741c58a4b5c91f9121e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/balanceTest", "targetIndexes": [44, 95]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-MinSizeRel-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [47, 96]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-MinSizeRel-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [50, 97]}, {"build": "Analysis_Robot/test/moistureAnalyzerDriverTest", "jsonFile": "directory-Analysis_Robot.test.moistureAnalyzerDriverTest-MinSizeRel-6df43ddc17b8d29e6a25.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "Analysis_Robot/test/moistureAnalyzerDriverTest", "targetIndexes": [52, 98]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-MinSizeRel-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer/APP", "targetIndexes": [55, 99]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-MinSizeRel-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Library", "targetIndexes": [58, 101]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-MinSizeRel-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/App", "targetIndexes": [0, 100]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-MinSizeRel-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [64, 102]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-MinSizeRel-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [67, 103]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-MinSizeRel-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [70, 104]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-MinSizeRel-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [73, 105]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-MinSizeRel-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [76, 106]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-MinSizeRel-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [79, 107]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-MinSizeRel-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [4, 114]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-MinSizeRel-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [13, 108]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-MinSizeRel-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [22, 109]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-MinSizeRel-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [31, 110]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-MinSizeRel-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [40, 111]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-MinSizeRel-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [12, 112]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-MinSizeRel-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "RoboticLaserMarking/UI", "targetIndexes": [57, 113]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-MinSizeRel-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [63, 115]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-MinSizeRel-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "fuxicore", "targetIndexes": [72, 138]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-MinSizeRel-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [81, 147]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-MinSizeRel-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_abb_socket", "targetIndexes": [28, 116]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-MinSizeRel-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_config_manager", "targetIndexes": [54, 117]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-MinSizeRel-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_csv", "targetIndexes": [78, 118]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-MinSizeRel-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_event_listener", "targetIndexes": [69, 119]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-MinSizeRel-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_executor", "targetIndexes": [46, 120]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-MinSizeRel-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_executor_context", "targetIndexes": [19, 121]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-MinSizeRel-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_fa2204n_balance", "targetIndexes": [61, 122]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-MinSizeRel-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [37, 123]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-MinSizeRel-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_fileutil", "targetIndexes": [10, 124]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-MinSizeRel-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_json", "targetIndexes": [75, 125]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-MinSizeRel-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_license_manager", "targetIndexes": [66, 126]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-MinSizeRel-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_license_ui", "targetIndexes": [60, 127]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-MinSizeRel-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_micro_dosing", "targetIndexes": [51, 155]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-MinSizeRel-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_network", "targetIndexes": [43, 128]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-MinSizeRel-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_serial", "targetIndexes": [34, 129]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-MinSizeRel-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_service_container", "targetIndexes": [25, 130]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-MinSizeRel-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_socket", "targetIndexes": [16, 131]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-MinSizeRel-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_sqlite", "targetIndexes": [7, 132]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-MinSizeRel-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "Test/test_taskflow", "targetIndexes": [80, 133]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-MinSizeRel-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "Test/test_twoaixsrobot", "targetIndexes": [77, 134]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-MinSizeRel-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "Test/test_xml", "targetIndexes": [74, 135]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-MinSizeRel-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [71, 139]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-MinSizeRel-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/AuboDriver", "targetIndexes": [68, 140]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-MinSizeRel-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [65, 141]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-MinSizeRel-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [62, 142]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-MinSizeRel-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [56, 143]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-MinSizeRel-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [53, 144]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-MinSizeRel-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/OpcDa", "targetIndexes": [3, 145]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-MinSizeRel-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/OpcUa", "targetIndexes": [2, 146]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-MinSizeRel-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/socket", "targetIndexes": [82, 153]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-MinSizeRel-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [6, 148]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-MinSizeRel-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [9, 149]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-MinSizeRel-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [49, 150]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-MinSizeRel-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "hardwaredriver/modbus", "targetIndexes": [15, 151]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-MinSizeRel-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "hardwaredriver/serial", "targetIndexes": [18, 152]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-MinSizeRel-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "hardwaredriver/usbcamera", "targetIndexes": [21, 154]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-MinSizeRel-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/calbuild", "targetIndexes": [24, 156]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-MinSizeRel-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/cameraCalibrator", "targetIndexes": [27, 158]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-MinSizeRel-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/caltest", "targetIndexes": [30, 157]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-MinSizeRel-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/communication", "targetIndexes": [33, 159]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-MinSizeRel-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecal", "targetIndexes": [36, 160]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-MinSizeRel-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaltest", "targetIndexes": [39, 161]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-MinSizeRel-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [42, 162]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-MinSizeRel-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 80, "source": "tool/handeyecaluipath", "targetIndexes": [45, 163]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-MinSizeRel-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 81, "source": "tool/handeyecaluipathAuto", "targetIndexes": [48, 164]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-MinSizeRel-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 82, "source": "tool/verify_calibration", "targetIndexes": [59, 165]}], "name": "MinSizeRel", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 136]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 137]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 85]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [11, 87]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 91]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 88]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 89]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 90]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 93]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 92]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 83]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 84]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 86]}, {"directoryIndexes": [13], "name": "Analysis_RobottestbalanceDriverTest", "parentIndex": 0, "targetIndexes": [41, 94]}, {"directoryIndexes": [14], "name": "Analysis_RobottestbalanceTest", "parentIndex": 0, "targetIndexes": [44, 95]}, {"directoryIndexes": [15], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [47, 96]}, {"directoryIndexes": [16], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [50, 97]}, {"directoryIndexes": [17], "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "parentIndex": 0, "targetIndexes": [52, 98]}, {"directoryIndexes": [18], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [55, 99]}, {"directoryIndexes": [19], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [58, 101]}, {"directoryIndexes": [20], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [0, 100]}, {"directoryIndexes": [21], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [64, 102]}, {"directoryIndexes": [22], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [67, 103]}, {"directoryIndexes": [23], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [70, 104]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [73, 105]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [76, 106]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [79, 107]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [4, 114]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [13, 108]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [22, 109]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [31, 110]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [40, 111]}, {"directoryIndexes": [32], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [12, 112]}, {"directoryIndexes": [33], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [57, 113]}, {"directoryIndexes": [34], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [63, 115]}, {"directoryIndexes": [35], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [72, 138]}, {"directoryIndexes": [36], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [81, 147]}, {"directoryIndexes": [37], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [28, 116]}, {"directoryIndexes": [38], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [54, 117]}, {"directoryIndexes": [39], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [78, 118]}, {"directoryIndexes": [40], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [69, 119]}, {"directoryIndexes": [41], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [46, 120]}, {"directoryIndexes": [42], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [19, 121]}, {"directoryIndexes": [43], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [61, 122]}, {"directoryIndexes": [44], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [37, 123]}, {"directoryIndexes": [45], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [10, 124]}, {"directoryIndexes": [46], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [75, 125]}, {"directoryIndexes": [47], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [66, 126]}, {"directoryIndexes": [48], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [60, 127]}, {"directoryIndexes": [49], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [51, 155]}, {"directoryIndexes": [50], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [43, 128]}, {"directoryIndexes": [51], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [34, 129]}, {"directoryIndexes": [52], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [25, 130]}, {"directoryIndexes": [53], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [16, 131]}, {"directoryIndexes": [54], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [7, 132]}, {"directoryIndexes": [55], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [80, 133]}, {"directoryIndexes": [56], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [77, 134]}, {"directoryIndexes": [57], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [74, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [71, 139]}, {"directoryIndexes": [59], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [68, 140]}, {"directoryIndexes": [60], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [65, 141]}, {"directoryIndexes": [61], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [62, 142]}, {"directoryIndexes": [62], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [56, 143]}, {"directoryIndexes": [63], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [53, 144]}, {"directoryIndexes": [64], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [3, 145]}, {"directoryIndexes": [65], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [2, 146]}, {"directoryIndexes": [66], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [82, 153]}, {"directoryIndexes": [67], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [6, 148]}, {"directoryIndexes": [68], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [9, 149]}, {"directoryIndexes": [69], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [49, 150]}, {"directoryIndexes": [70], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [15, 151]}, {"directoryIndexes": [71], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [21, 154]}, {"directoryIndexes": [73], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [24, 156]}, {"directoryIndexes": [74], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [27, 158]}, {"directoryIndexes": [75], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [30, 157]}, {"directoryIndexes": [76], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [33, 159]}, {"directoryIndexes": [77], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [36, 160]}, {"directoryIndexes": [78], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [39, 161]}, {"directoryIndexes": [79], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [42, 162]}, {"directoryIndexes": [80], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [45, 163]}, {"directoryIndexes": [81], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [48, 164]}, {"directoryIndexes": [82], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [59, 165]}], "targets": [{"directoryIndex": 20, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-MinSizeRel-85f9776f404a9d04d70a.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-48c30841ee9a4a3d95b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 65, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-MinSizeRel-f4b1615a7a5f556f591f.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 64, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-MinSizeRel-9a5f96b8c74a723de6cc.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 27, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-MinSizeRel-8ed5b5467f5c79f1561f.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-MinSizeRel-33c83275e6e017ead8c0.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 67, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-MinSizeRel-0963848655f3ab3e2fd2.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 54, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-MinSizeRel-285251f63eda448c4207.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-MinSizeRel-5a96df7c6ffc74b0fbb3.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 68, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-MinSizeRel-5532b6fa9f76d055fb06.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 45, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-MinSizeRel-a5231ac37c8cf35ee33f.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-MinSizeRel-3c4246728da31ae807d7.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 32, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-MinSizeRel-abd4d9488c540bfc51e9.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 28, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-MinSizeRel-82d79d319fe701d5945b.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-MinSizeRel-f79af737752433390703.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-MinSizeRel-f517c18acd3e9be18b13.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 53, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-MinSizeRel-8b3dbcab358c45534be6.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-MinSizeRel-e7a1b0e529138820a42f.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-MinSizeRel-c72ba7438a0ccd6ffa04.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 42, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-MinSizeRel-8e4216a5c53dd1a0c6f3.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-MinSizeRel-6352b9d286d39318039f.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-MinSizeRel-13d256b4d376dcd56476.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 29, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-MinSizeRel-a12e7c36da385a540597.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-MinSizeRel-015880a41437497ef3b7.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 73, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-MinSizeRel-9355cfe3a413a237237d.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 52, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-MinSizeRel-9716f0a05fbb83bbb3bd.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-MinSizeRel-df3268a84b2aed103915.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 74, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-MinSizeRel-dfadcaeeba9ba64bed1e.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 37, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-MinSizeRel-a8f6f57345b3841fd693.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-MinSizeRel-2730ff5697c12566c597.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 75, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-MinSizeRel-15c591b4f06f78385e7a.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 30, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-MinSizeRel-2e1ecc39902bf749266f.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-MinSizeRel-5baf0da4e402fa264c0b.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-MinSizeRel-7e206d9c856c97db078a.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 51, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-MinSizeRel-dcc410c1d8b0e69cd111.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-MinSizeRel-13b772661bc830f004b9.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-MinSizeRel-bf3efcfdc18b56070539.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 44, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-MinSizeRel-2e1bb9c077e306455584.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-MinSizeRel-03c5be1bc5bd216d3958.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-MinSizeRel-ac7845ad0cc7df660bc4.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 31, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-MinSizeRel-9ac4daac53e8de4b0373.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 13, "id": "ALL_BUILD::@f2032f6c36bb657d8ab6", "jsonFile": "target-ALL_BUILD-MinSizeRel-7a5251c4190b7ebcb7d2.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-MinSizeRel-b8867bbeaf0f6148c31f.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 50, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-MinSizeRel-6b5ac6e9946f6ca19396.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 14, "id": "ALL_BUILD::@67fcef0db64755a7efa1", "jsonFile": "target-ALL_BUILD-MinSizeRel-902e971a81d0d055fcd1.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 80, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-MinSizeRel-54f6bda5ac93bf0a16e1.json", "name": "ALL_BUILD", "projectIndex": 80}, {"directoryIndex": 41, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-MinSizeRel-179f33c45a88edf1cc65.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 15, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-MinSizeRel-8b2129794a5b7213244f.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 81, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-MinSizeRel-017bc66adac4d25af138.json", "name": "ALL_BUILD", "projectIndex": 81}, {"directoryIndex": 69, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-MinSizeRel-e7dc7b5610988139a6bf.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 16, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-MinSizeRel-89a06e46db00817d896d.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 49, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-MinSizeRel-25ccdf49d4f252d4663c.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 17, "id": "ALL_BUILD::@344f5fe5f7bc7b70cade", "jsonFile": "target-ALL_BUILD-MinSizeRel-b028a33ff2ea2898cc11.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 63, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-MinSizeRel-95a0d049fbb3ffaaca04.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 38, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-MinSizeRel-76ec84edc0fb36542d20.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 18, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-MinSizeRel-4bd9588263a2e6bc50cf.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 62, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-MinSizeRel-b1f9058584bf0a918bba.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 33, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-MinSizeRel-661f08955d464024345a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 19, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-MinSizeRel-39921dc5635a199dc5c3.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 82, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-MinSizeRel-51a0fa8c48f3509ec23a.json", "name": "ALL_BUILD", "projectIndex": 82}, {"directoryIndex": 48, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-MinSizeRel-24975862660e8c214b60.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 43, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-MinSizeRel-7c47365a41fa76d4dea2.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 61, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-MinSizeRel-bbe04bcf2e657f51af19.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 34, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-MinSizeRel-a6ab1cea57c62f3e1a60.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 21, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-MinSizeRel-8e59bdd4027a8bf10ae5.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 60, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-MinSizeRel-b74498e8f63e9b1b74ac.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 47, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-MinSizeRel-6933b8546b999c09fb7c.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 22, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-MinSizeRel-8397406cb0e0cf7992b8.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 59, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-MinSizeRel-ef0da601182b6b6b8f17.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 40, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-MinSizeRel-3bf19658289dd65e46ef.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 23, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-MinSizeRel-cf52e46cdfb4ab946667.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 58, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-MinSizeRel-5110c23ae1c198d6bda3.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 35, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-MinSizeRel-569802fed0e125d31ab4.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 24, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-MinSizeRel-adbed5082fd27d9fb126.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 57, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-MinSizeRel-7f01a577f19cd6b318f3.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 46, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-MinSizeRel-e6a290df2ee065fd2889.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 25, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-MinSizeRel-a521958a0993fcac108a.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 56, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-MinSizeRel-d6bc1818314a184f1537.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 39, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-MinSizeRel-7ffa9bfdcce8201ba5d2.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 26, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-MinSizeRel-e64cb122a0640fbfbfd4.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 55, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-MinSizeRel-7e98f33b92a345e5c7b6.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 36, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-MinSizeRel-afdcfcd02f0d3fc4b858.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 66, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-MinSizeRel-d5422a0ec356725cf693.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-MinSizeRel-d64d134bb8b3306cb0dc.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-MinSizeRel-e943f03434cfc60fbf6f.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-MinSizeRel-0fd89dfecd23f8be7f45.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-MinSizeRel-432fbd553f4f117a7aa1.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-MinSizeRel-55cff2964b6671f48933.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-MinSizeRel-e7827f2771ecaa51717d.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-MinSizeRel-da938890489dd639e5ca.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-MinSizeRel-5e5664e2f81d011e56ee.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-MinSizeRel-4b9ac15714dcbc658654.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-MinSizeRel-34beb5ce77b24d31df4f.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-MinSizeRel-72c5640eb3fc53806e19.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestbalanceDriverTest::@f2032f6c36bb657d8ab6", "jsonFile": "target-Analysis_RobottestbalanceDriverTest-MinSizeRel-a81f7e3085942ba95f7c.json", "name": "Analysis_RobottestbalanceDriverTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestbalanceTest::@67fcef0db64755a7efa1", "jsonFile": "target-Analysis_RobottestbalanceTest-MinSizeRel-0d2c7f72183f7405a984.json", "name": "Analysis_RobottestbalanceTest", "projectIndex": 14}, {"directoryIndex": 15, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-MinSizeRel-6e8a5a0316d5034fff20.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 15}, {"directoryIndex": 16, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-MinSizeRel-78e50254d2c87cf04357.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 16}, {"directoryIndex": 17, "id": "Analysis_RobottestmoistureAnalyzerDriverTest::@344f5fe5f7bc7b70cade", "jsonFile": "target-Analysis_RobottestmoistureAnalyzerDriverTest-MinSizeRel-a5c0b5ab544836863e44.json", "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "projectIndex": 17}, {"directoryIndex": 18, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-MinSizeRel-fb5048985a48bed84fc8.json", "name": "MJServerAPP", "projectIndex": 18}, {"directoryIndex": 20, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-MinSizeRel-0ca99a0bcb9d7b9b8e68.json", "name": "MJServer_RefactorApp", "projectIndex": 20}, {"directoryIndex": 19, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-MinSizeRel-b970bb746d85f60b0879.json", "name": "MJServer_RefactorLibrary", "projectIndex": 19}, {"directoryIndex": 21, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-MinSizeRel-3ed17a32527d31bfdfe3.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 21}, {"directoryIndex": 22, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-MinSizeRel-426f40a1481de1c8c7c2.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 22}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-MinSizeRel-19fe117bf0ccd2f75ec2.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 23}, {"directoryIndex": 24, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-MinSizeRel-64c7096d645e3de13080.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 24}, {"directoryIndex": 25, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-MinSizeRel-c0c2de1aabf9f1d7bef3.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-MinSizeRel-6cc03294da3fe2b814b4.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 26}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-MinSizeRel-0f09ca872640188ab53d.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-MinSizeRel-06f0bbac842f2c092557.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-MinSizeRel-c68c1f90595ea7bd5ea8.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 30}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-MinSizeRel-fa9939ea1e3820e71f80.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 31}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-MinSizeRel-088f7264edc3b0fbdc9b.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 32}, {"directoryIndex": 33, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-MinSizeRel-3dbbb56ce036a25a820b.json", "name": "RoboticLaserMarkingUI", "projectIndex": 33}, {"directoryIndex": 27, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-MinSizeRel-eed51488ef878a303dd3.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 27}, {"directoryIndex": 34, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-MinSizeRel-9729e52ad24c214ed0a5.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 34}, {"directoryIndex": 37, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-MinSizeRel-c6494b47fc63cc6c344b.json", "name": "Testtest_abb_socket", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-MinSizeRel-e43982c33affab328370.json", "name": "Testtest_config_manager", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-MinSizeRel-1843dc9a758a6ebceab4.json", "name": "Testtest_csv", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-MinSizeRel-c7933304a6d33a30ef28.json", "name": "Testtest_event_listener", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-MinSizeRel-02044e4a75c06b422477.json", "name": "Testtest_executor", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-MinSizeRel-a8e700f9bd773a0c96ec.json", "name": "Testtest_executor_context", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-MinSizeRel-fb864a041ea28a243b76.json", "name": "Testtest_fa2204n_balance", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-MinSizeRel-04779eb53d2b9294df41.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-MinSizeRel-bdbf95d9d3154fec5677.json", "name": "Testtest_fileutil", "projectIndex": 45}, {"directoryIndex": 46, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-MinSizeRel-ec18bc0e35cd6371781e.json", "name": "Testtest_json", "projectIndex": 46}, {"directoryIndex": 47, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-MinSizeRel-e39f901e69a16c3e4ef8.json", "name": "Testtest_license_manager", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-MinSizeRel-9bcdbe3c1100d0215631.json", "name": "Testtest_license_ui", "projectIndex": 48}, {"directoryIndex": 50, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-MinSizeRel-836ce71c2efa0380e6e1.json", "name": "Testtest_network", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-MinSizeRel-5a86917dcbe518ad8756.json", "name": "Testtest_serial", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-MinSizeRel-ccf065709cb1e297c09b.json", "name": "Testtest_service_container", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-MinSizeRel-c5ecdb2e28a56ad45f1e.json", "name": "Testtest_socket", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-MinSizeRel-ca5351ab004dc7817ae7.json", "name": "Testtest_sqlite", "projectIndex": 54}, {"directoryIndex": 55, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-MinSizeRel-0790ffb95eda769b5820.json", "name": "Testtest_taskflow", "projectIndex": 55}, {"directoryIndex": 56, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-MinSizeRel-60621fafb179d923f40d.json", "name": "Testtest_twoaixsrobot", "projectIndex": 56}, {"directoryIndex": 57, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-MinSizeRel-b09babd292606494f855.json", "name": "Testtest_xml", "projectIndex": 57}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-MinSizeRel-6b2f2172edce8be57525.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 35, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-MinSizeRel-5a0e77ae955246d7464f.json", "name": "fuxicore", "projectIndex": 35}, {"directoryIndex": 58, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-MinSizeRel-302e4b8544f0032763ce.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-MinSizeRel-91e2392a41de16ab2854.json", "name": "hardwaredriverAuboDriver", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-MinSizeRel-64ef091ff85f1ea18090.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-MinSizeRel-fdeffc926a073d0fb254.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-MinSizeRel-172eff0c4327dace7dca.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 62}, {"directoryIndex": 63, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-MinSizeRel-347bb441141203443bd4.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 63}, {"directoryIndex": 64, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-MinSizeRel-dd8a3e30eca065137ad3.json", "name": "hardwaredriverOpcDa", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-MinSizeRel-ece40cde72a7884a03e0.json", "name": "hardwaredriverOpcUa", "projectIndex": 65}, {"directoryIndex": 36, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-MinSizeRel-c1f079919721c0200c39.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 36}, {"directoryIndex": 67, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-MinSizeRel-1c0ab95437d19c953640.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-MinSizeRel-97aaa402880f338af091.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 68}, {"directoryIndex": 69, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-MinSizeRel-7600e2aab0e3b424a8be.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 69}, {"directoryIndex": 70, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-MinSizeRel-651fb0239d8ca36462c9.json", "name": "hardwaredrivermodbus", "projectIndex": 70}, {"directoryIndex": 71, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-MinSizeRel-d965d5964efeacc8566c.json", "name": "hardwaredriverserial", "projectIndex": 71}, {"directoryIndex": 66, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-MinSizeRel-b0fc4c743012d07721e9.json", "name": "hardwaredriversocket", "projectIndex": 66}, {"directoryIndex": 72, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-MinSizeRel-677951234ea2898117f1.json", "name": "hardwaredriverusbcamera", "projectIndex": 72}, {"directoryIndex": 49, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-MinSizeRel-f2bdd29c8dcc6a87fe3b.json", "name": "test_micro_dosing", "projectIndex": 49}, {"directoryIndex": 73, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-MinSizeRel-3c6aedcc93e084dc4ba2.json", "name": "toolcalbuild", "projectIndex": 73}, {"directoryIndex": 75, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-MinSizeRel-44164ea36ef9be28ba06.json", "name": "toolcaltest", "projectIndex": 75}, {"directoryIndex": 74, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-MinSizeRel-d243c6aca274678d7434.json", "name": "toolcameraCalibrator", "projectIndex": 74}, {"directoryIndex": 76, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-MinSizeRel-4641000daa968c87f2b1.json", "name": "toolcommunication", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-MinSizeRel-0ad93393dfc3db9445b9.json", "name": "toolhandeyecal", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-MinSizeRel-b20c36cbf0e84879d8a0.json", "name": "toolhandeyecaltest", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-MinSizeRel-e104da8422f559dd5508.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 79}, {"directoryIndex": 80, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-MinSizeRel-28a0520a34065ff3cdeb.json", "name": "toolhandeyecaluipath", "projectIndex": 80}, {"directoryIndex": 81, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-MinSizeRel-d3879b98e791e52c00cb.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 81}, {"directoryIndex": 82, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-MinSizeRel-aa4111a3cf5dd2cd6de1.json", "name": "toolverify_calibration", "projectIndex": 82}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 136]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-RelWithDebInfo-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 137]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-RelWithDebInfo-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 85]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-RelWithDebInfo-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [11, 87]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-RelWithDebInfo-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 91]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-RelWithDebInfo-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 88]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-RelWithDebInfo-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 89]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-RelWithDebInfo-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 90]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-RelWithDebInfo-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 93]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-RelWithDebInfo-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 92]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-RelWithDebInfo-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 83]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-RelWithDebInfo-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 84]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-RelWithDebInfo-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 86]}, {"build": "Analysis_Robot/test/balanceDriverTest", "jsonFile": "directory-Analysis_Robot.test.balanceDriverTest-RelWithDebInfo-7e4f5522755cfdc4e01f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/balanceDriverTest", "targetIndexes": [41, 94]}, {"build": "Analysis_Robot/test/balanceTest", "jsonFile": "directory-Analysis_Robot.test.balanceTest-RelWithDebInfo-4741c58a4b5c91f9121e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/balanceTest", "targetIndexes": [44, 95]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-RelWithDebInfo-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [47, 96]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-RelWithDebInfo-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [50, 97]}, {"build": "Analysis_Robot/test/moistureAnalyzerDriverTest", "jsonFile": "directory-Analysis_Robot.test.moistureAnalyzerDriverTest-RelWithDebInfo-6df43ddc17b8d29e6a25.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "Analysis_Robot/test/moistureAnalyzerDriverTest", "targetIndexes": [52, 98]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-RelWithDebInfo-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer/APP", "targetIndexes": [55, 99]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-RelWithDebInfo-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Library", "targetIndexes": [58, 101]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-RelWithDebInfo-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/App", "targetIndexes": [0, 100]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-RelWithDebInfo-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [64, 102]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-RelWithDebInfo-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [67, 103]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-RelWithDebInfo-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [70, 104]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-RelWithDebInfo-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [73, 105]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-RelWithDebInfo-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [76, 106]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-RelWithDebInfo-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [79, 107]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-RelWithDebInfo-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [4, 114]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-RelWithDebInfo-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [13, 108]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-RelWithDebInfo-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [22, 109]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-RelWithDebInfo-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [31, 110]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-RelWithDebInfo-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [40, 111]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-RelWithDebInfo-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [12, 112]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-RelWithDebInfo-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "RoboticLaserMarking/UI", "targetIndexes": [57, 113]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-RelWithDebInfo-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [63, 115]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-RelWithDebInfo-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "fuxicore", "targetIndexes": [72, 138]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-RelWithDebInfo-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [81, 147]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-RelWithDebInfo-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_abb_socket", "targetIndexes": [28, 116]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-RelWithDebInfo-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_config_manager", "targetIndexes": [54, 117]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-RelWithDebInfo-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_csv", "targetIndexes": [78, 118]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-RelWithDebInfo-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_event_listener", "targetIndexes": [69, 119]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-RelWithDebInfo-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_executor", "targetIndexes": [46, 120]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-RelWithDebInfo-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_executor_context", "targetIndexes": [19, 121]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-RelWithDebInfo-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_fa2204n_balance", "targetIndexes": [61, 122]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-RelWithDebInfo-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [37, 123]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-RelWithDebInfo-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_fileutil", "targetIndexes": [10, 124]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-RelWithDebInfo-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_json", "targetIndexes": [75, 125]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-RelWithDebInfo-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_license_manager", "targetIndexes": [66, 126]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-RelWithDebInfo-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_license_ui", "targetIndexes": [60, 127]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-RelWithDebInfo-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_micro_dosing", "targetIndexes": [51, 155]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-RelWithDebInfo-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_network", "targetIndexes": [43, 128]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-RelWithDebInfo-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_serial", "targetIndexes": [34, 129]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-RelWithDebInfo-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_service_container", "targetIndexes": [25, 130]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-RelWithDebInfo-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_socket", "targetIndexes": [16, 131]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-RelWithDebInfo-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_sqlite", "targetIndexes": [7, 132]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-RelWithDebInfo-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "Test/test_taskflow", "targetIndexes": [80, 133]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-RelWithDebInfo-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "Test/test_twoaixsrobot", "targetIndexes": [77, 134]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-RelWithDebInfo-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "Test/test_xml", "targetIndexes": [74, 135]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-RelWithDebInfo-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [71, 139]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-RelWithDebInfo-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/AuboDriver", "targetIndexes": [68, 140]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-RelWithDebInfo-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [65, 141]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-RelWithDebInfo-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [62, 142]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-RelWithDebInfo-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [56, 143]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-RelWithDebInfo-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [53, 144]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-RelWithDebInfo-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/OpcDa", "targetIndexes": [3, 145]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-RelWithDebInfo-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/OpcUa", "targetIndexes": [2, 146]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-RelWithDebInfo-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/socket", "targetIndexes": [82, 153]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-RelWithDebInfo-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [6, 148]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-RelWithDebInfo-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [9, 149]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-RelWithDebInfo-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [49, 150]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-RelWithDebInfo-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "hardwaredriver/modbus", "targetIndexes": [15, 151]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-RelWithDebInfo-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "hardwaredriver/serial", "targetIndexes": [18, 152]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-RelWithDebInfo-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "hardwaredriver/usbcamera", "targetIndexes": [21, 154]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-RelWithDebInfo-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/calbuild", "targetIndexes": [24, 156]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-RelWithDebInfo-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/cameraCalibrator", "targetIndexes": [27, 158]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-RelWithDebInfo-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/caltest", "targetIndexes": [30, 157]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-RelWithDebInfo-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/communication", "targetIndexes": [33, 159]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-RelWithDebInfo-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecal", "targetIndexes": [36, 160]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-RelWithDebInfo-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaltest", "targetIndexes": [39, 161]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-RelWithDebInfo-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [42, 162]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-RelWithDebInfo-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 80, "source": "tool/handeyecaluipath", "targetIndexes": [45, 163]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-RelWithDebInfo-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 81, "source": "tool/handeyecaluipathAuto", "targetIndexes": [48, 164]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-RelWithDebInfo-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 82, "source": "tool/verify_calibration", "targetIndexes": [59, 165]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 136]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 137]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 85]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [11, 87]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 91]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 88]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 89]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 90]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 93]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 92]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 83]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 84]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 86]}, {"directoryIndexes": [13], "name": "Analysis_RobottestbalanceDriverTest", "parentIndex": 0, "targetIndexes": [41, 94]}, {"directoryIndexes": [14], "name": "Analysis_RobottestbalanceTest", "parentIndex": 0, "targetIndexes": [44, 95]}, {"directoryIndexes": [15], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [47, 96]}, {"directoryIndexes": [16], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [50, 97]}, {"directoryIndexes": [17], "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "parentIndex": 0, "targetIndexes": [52, 98]}, {"directoryIndexes": [18], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [55, 99]}, {"directoryIndexes": [19], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [58, 101]}, {"directoryIndexes": [20], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [0, 100]}, {"directoryIndexes": [21], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [64, 102]}, {"directoryIndexes": [22], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [67, 103]}, {"directoryIndexes": [23], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [70, 104]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [73, 105]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [76, 106]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [79, 107]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [4, 114]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [13, 108]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [22, 109]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [31, 110]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [40, 111]}, {"directoryIndexes": [32], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [12, 112]}, {"directoryIndexes": [33], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [57, 113]}, {"directoryIndexes": [34], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [63, 115]}, {"directoryIndexes": [35], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [72, 138]}, {"directoryIndexes": [36], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [81, 147]}, {"directoryIndexes": [37], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [28, 116]}, {"directoryIndexes": [38], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [54, 117]}, {"directoryIndexes": [39], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [78, 118]}, {"directoryIndexes": [40], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [69, 119]}, {"directoryIndexes": [41], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [46, 120]}, {"directoryIndexes": [42], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [19, 121]}, {"directoryIndexes": [43], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [61, 122]}, {"directoryIndexes": [44], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [37, 123]}, {"directoryIndexes": [45], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [10, 124]}, {"directoryIndexes": [46], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [75, 125]}, {"directoryIndexes": [47], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [66, 126]}, {"directoryIndexes": [48], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [60, 127]}, {"directoryIndexes": [49], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [51, 155]}, {"directoryIndexes": [50], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [43, 128]}, {"directoryIndexes": [51], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [34, 129]}, {"directoryIndexes": [52], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [25, 130]}, {"directoryIndexes": [53], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [16, 131]}, {"directoryIndexes": [54], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [7, 132]}, {"directoryIndexes": [55], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [80, 133]}, {"directoryIndexes": [56], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [77, 134]}, {"directoryIndexes": [57], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [74, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [71, 139]}, {"directoryIndexes": [59], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [68, 140]}, {"directoryIndexes": [60], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [65, 141]}, {"directoryIndexes": [61], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [62, 142]}, {"directoryIndexes": [62], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [56, 143]}, {"directoryIndexes": [63], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [53, 144]}, {"directoryIndexes": [64], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [3, 145]}, {"directoryIndexes": [65], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [2, 146]}, {"directoryIndexes": [66], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [82, 153]}, {"directoryIndexes": [67], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [6, 148]}, {"directoryIndexes": [68], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [9, 149]}, {"directoryIndexes": [69], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [49, 150]}, {"directoryIndexes": [70], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [15, 151]}, {"directoryIndexes": [71], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [21, 154]}, {"directoryIndexes": [73], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [24, 156]}, {"directoryIndexes": [74], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [27, 158]}, {"directoryIndexes": [75], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [30, 157]}, {"directoryIndexes": [76], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [33, 159]}, {"directoryIndexes": [77], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [36, 160]}, {"directoryIndexes": [78], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [39, 161]}, {"directoryIndexes": [79], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [42, 162]}, {"directoryIndexes": [80], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [45, 163]}, {"directoryIndexes": [81], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [48, 164]}, {"directoryIndexes": [82], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [59, 165]}], "targets": [{"directoryIndex": 20, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-85f9776f404a9d04d70a.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-48c30841ee9a4a3d95b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 65, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f4b1615a7a5f556f591f.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 64, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9a5f96b8c74a723de6cc.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 27, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8ed5b5467f5c79f1561f.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-33c83275e6e017ead8c0.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 67, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0963848655f3ab3e2fd2.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 54, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-285251f63eda448c4207.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5a96df7c6ffc74b0fbb3.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 68, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5532b6fa9f76d055fb06.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 45, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a5231ac37c8cf35ee33f.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3c4246728da31ae807d7.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 32, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-abd4d9488c540bfc51e9.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 28, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-82d79d319fe701d5945b.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f79af737752433390703.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f517c18acd3e9be18b13.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 53, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8b3dbcab358c45534be6.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-e7a1b0e529138820a42f.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c72ba7438a0ccd6ffa04.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 42, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8e4216a5c53dd1a0c6f3.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-6352b9d286d39318039f.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-13d256b4d376dcd56476.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 29, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a12e7c36da385a540597.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-015880a41437497ef3b7.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 73, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9355cfe3a413a237237d.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 52, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9716f0a05fbb83bbb3bd.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-df3268a84b2aed103915.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 74, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-dfadcaeeba9ba64bed1e.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 37, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a8f6f57345b3841fd693.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2730ff5697c12566c597.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 75, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-15c591b4f06f78385e7a.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 30, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2e1ecc39902bf749266f.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5baf0da4e402fa264c0b.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7e206d9c856c97db078a.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 51, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-dcc410c1d8b0e69cd111.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-13b772661bc830f004b9.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-bf3efcfdc18b56070539.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 44, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2e1bb9c077e306455584.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-03c5be1bc5bd216d3958.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-ac7845ad0cc7df660bc4.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 31, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9ac4daac53e8de4b0373.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 13, "id": "ALL_BUILD::@f2032f6c36bb657d8ab6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7a5251c4190b7ebcb7d2.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b8867bbeaf0f6148c31f.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 50, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-6b5ac6e9946f6ca19396.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 14, "id": "ALL_BUILD::@67fcef0db64755a7efa1", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-902e971a81d0d055fcd1.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 80, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-54f6bda5ac93bf0a16e1.json", "name": "ALL_BUILD", "projectIndex": 80}, {"directoryIndex": 41, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-179f33c45a88edf1cc65.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 15, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8b2129794a5b7213244f.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 81, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-017bc66adac4d25af138.json", "name": "ALL_BUILD", "projectIndex": 81}, {"directoryIndex": 69, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-e7dc7b5610988139a6bf.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 16, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-89a06e46db00817d896d.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 49, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-25ccdf49d4f252d4663c.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 17, "id": "ALL_BUILD::@344f5fe5f7bc7b70cade", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b028a33ff2ea2898cc11.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 63, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-95a0d049fbb3ffaaca04.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 38, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-76ec84edc0fb36542d20.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 18, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-4bd9588263a2e6bc50cf.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 62, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b1f9058584bf0a918bba.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 33, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-661f08955d464024345a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 19, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-39921dc5635a199dc5c3.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 82, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-51a0fa8c48f3509ec23a.json", "name": "ALL_BUILD", "projectIndex": 82}, {"directoryIndex": 48, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-24975862660e8c214b60.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 43, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7c47365a41fa76d4dea2.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 61, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-bbe04bcf2e657f51af19.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 34, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a6ab1cea57c62f3e1a60.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 21, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8e59bdd4027a8bf10ae5.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 60, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b74498e8f63e9b1b74ac.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 47, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-6933b8546b999c09fb7c.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 22, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8397406cb0e0cf7992b8.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 59, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-ef0da601182b6b6b8f17.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 40, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3bf19658289dd65e46ef.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 23, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-cf52e46cdfb4ab946667.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 58, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5110c23ae1c198d6bda3.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 35, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-569802fed0e125d31ab4.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 24, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-adbed5082fd27d9fb126.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 57, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7f01a577f19cd6b318f3.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 46, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-e6a290df2ee065fd2889.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 25, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a521958a0993fcac108a.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 56, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d6bc1818314a184f1537.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 39, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7ffa9bfdcce8201ba5d2.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 26, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-e64cb122a0640fbfbfd4.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 55, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7e98f33b92a345e5c7b6.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 36, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-afdcfcd02f0d3fc4b858.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 66, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d5422a0ec356725cf693.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-RelWithDebInfo-4494440ba021a4ad1269.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-RelWithDebInfo-fbff802dd6f87d64593b.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-RelWithDebInfo-dfdbc540ca51e3859e4c.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-RelWithDebInfo-c8a80c033040d986739f.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-RelWithDebInfo-50256aa62661203bd4a5.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-RelWithDebInfo-4d468d1e2be46b15e628.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-RelWithDebInfo-00d538fa5dd0b627565e.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-RelWithDebInfo-ce15f6eaada500283e2c.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-RelWithDebInfo-10cc8420aaac4ccbbb52.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-RelWithDebInfo-78435fdf35b986ba7c1c.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-RelWithDebInfo-e2c8d97154db8e42b532.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestbalanceDriverTest::@f2032f6c36bb657d8ab6", "jsonFile": "target-Analysis_RobottestbalanceDriverTest-RelWithDebInfo-2e044934fd886403cf4b.json", "name": "Analysis_RobottestbalanceDriverTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestbalanceTest::@67fcef0db64755a7efa1", "jsonFile": "target-Analysis_RobottestbalanceTest-RelWithDebInfo-c5acda77d98c41285460.json", "name": "Analysis_RobottestbalanceTest", "projectIndex": 14}, {"directoryIndex": 15, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-RelWithDebInfo-318ea14a4a7795d025f6.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 15}, {"directoryIndex": 16, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-RelWithDebInfo-49b9e1979f5fee719114.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 16}, {"directoryIndex": 17, "id": "Analysis_RobottestmoistureAnalyzerDriverTest::@344f5fe5f7bc7b70cade", "jsonFile": "target-Analysis_RobottestmoistureAnalyzerDriverTest-RelWithDebInfo-8d8fee781e4eb679efbd.json", "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "projectIndex": 17}, {"directoryIndex": 18, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-RelWithDebInfo-aa910c090415d2f3a1bb.json", "name": "MJServerAPP", "projectIndex": 18}, {"directoryIndex": 20, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-RelWithDebInfo-dd73f603607b6f2d53b5.json", "name": "MJServer_RefactorApp", "projectIndex": 20}, {"directoryIndex": 19, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-RelWithDebInfo-e134025ffb009b634f8b.json", "name": "MJServer_RefactorLibrary", "projectIndex": 19}, {"directoryIndex": 21, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-RelWithDebInfo-c8f838e97023ce48dfba.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 21}, {"directoryIndex": 22, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-RelWithDebInfo-a55d31c44e99bbc058aa.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 22}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-RelWithDebInfo-6094230cec49e6a45c3d.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 23}, {"directoryIndex": 24, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-RelWithDebInfo-277a0014ebb3f6cadb6b.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 24}, {"directoryIndex": 25, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-RelWithDebInfo-c5de8e2af1a91c9e3368.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-RelWithDebInfo-a6e61024ac60a45d5f3e.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 26}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-RelWithDebInfo-ef7c4916b8dcef92b2bc.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-RelWithDebInfo-20cedf0b037974e60807.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-RelWithDebInfo-c4a39d6218f2b433583e.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 30}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-RelWithDebInfo-485096d990813805214a.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 31}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-RelWithDebInfo-e45d6e534263f7e78b7e.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 32}, {"directoryIndex": 33, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-RelWithDebInfo-eca218de3249d8177b37.json", "name": "RoboticLaserMarkingUI", "projectIndex": 33}, {"directoryIndex": 27, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-RelWithDebInfo-feb48440dd467304bee3.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 27}, {"directoryIndex": 34, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-RelWithDebInfo-83b81d3f2378cc832082.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 34}, {"directoryIndex": 37, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-RelWithDebInfo-55631f2bffeff30226b6.json", "name": "Testtest_abb_socket", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-RelWithDebInfo-506d70a32e93c37d99c9.json", "name": "Testtest_config_manager", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-RelWithDebInfo-974f046f045d4b9c3dc6.json", "name": "Testtest_csv", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-RelWithDebInfo-861e380f716e15ba8f72.json", "name": "Testtest_event_listener", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-RelWithDebInfo-b9605db3f2bff07bfa5c.json", "name": "Testtest_executor", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-RelWithDebInfo-fe56e02644382dc03628.json", "name": "Testtest_executor_context", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-RelWithDebInfo-0a641512370942fc1bc9.json", "name": "Testtest_fa2204n_balance", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-RelWithDebInfo-96165f5d542686f33156.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-RelWithDebInfo-e113919b89b646ede21b.json", "name": "Testtest_fileutil", "projectIndex": 45}, {"directoryIndex": 46, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-RelWithDebInfo-30295b2928a097c40cd4.json", "name": "Testtest_json", "projectIndex": 46}, {"directoryIndex": 47, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-RelWithDebInfo-117df54cf66d2a96fc1b.json", "name": "Testtest_license_manager", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-RelWithDebInfo-3503eed2d0b93f8ae4f6.json", "name": "Testtest_license_ui", "projectIndex": 48}, {"directoryIndex": 50, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-RelWithDebInfo-b4910029b2ad11b183e3.json", "name": "Testtest_network", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-RelWithDebInfo-f2605c87bbdf9a9de892.json", "name": "Testtest_serial", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-RelWithDebInfo-b9b9ebc5e708d759652e.json", "name": "Testtest_service_container", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-RelWithDebInfo-e0c06f5549a1c04e9b88.json", "name": "Testtest_socket", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-RelWithDebInfo-0126e3ad44ef86c9bd8f.json", "name": "Testtest_sqlite", "projectIndex": 54}, {"directoryIndex": 55, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-RelWithDebInfo-a6162bdf0b0d46c65ec3.json", "name": "Testtest_taskflow", "projectIndex": 55}, {"directoryIndex": 56, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-RelWithDebInfo-1eaaad9d52ee7daea96d.json", "name": "Testtest_twoaixsrobot", "projectIndex": 56}, {"directoryIndex": 57, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-RelWithDebInfo-56d517da18b69f99dab0.json", "name": "Testtest_xml", "projectIndex": 57}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-RelWithDebInfo-cc9537d58aae9a92a63e.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 35, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-RelWithDebInfo-a67b33d22722e5f9440a.json", "name": "fuxicore", "projectIndex": 35}, {"directoryIndex": 58, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-RelWithDebInfo-4ec65358d1b2da94b868.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-RelWithDebInfo-da3af842a49734f06fcc.json", "name": "hardwaredriverAuboDriver", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-RelWithDebInfo-eab92edc41032b07f775.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-RelWithDebInfo-17471df87c3f1b08482e.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-RelWithDebInfo-0a10094cfd619e3b9548.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 62}, {"directoryIndex": 63, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-RelWithDebInfo-33016b51de8d817a3c82.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 63}, {"directoryIndex": 64, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-RelWithDebInfo-56a2dd44eb69f4633f30.json", "name": "hardwaredriverOpcDa", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-RelWithDebInfo-b15ce1aecbfdf204cd7a.json", "name": "hardwaredriverOpcUa", "projectIndex": 65}, {"directoryIndex": 36, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-RelWithDebInfo-089382f67d70d3424d48.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 36}, {"directoryIndex": 67, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-RelWithDebInfo-c7cb8e4b844da5fe0625.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-RelWithDebInfo-c00a57b62b32a8623466.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 68}, {"directoryIndex": 69, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-RelWithDebInfo-e8e713037bb48f0345a1.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 69}, {"directoryIndex": 70, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-RelWithDebInfo-8048763c7b36c4fb0f52.json", "name": "hardwaredrivermodbus", "projectIndex": 70}, {"directoryIndex": 71, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-RelWithDebInfo-1c434d795ffb55be9f8f.json", "name": "hardwaredriverserial", "projectIndex": 71}, {"directoryIndex": 66, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-RelWithDebInfo-240c979dd24f32960497.json", "name": "hardwaredriversocket", "projectIndex": 66}, {"directoryIndex": 72, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-RelWithDebInfo-425f39c8720b4375f0b1.json", "name": "hardwaredriverusbcamera", "projectIndex": 72}, {"directoryIndex": 49, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-RelWithDebInfo-2344b49225dd52c0d1f0.json", "name": "test_micro_dosing", "projectIndex": 49}, {"directoryIndex": 73, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-RelWithDebInfo-ba29d235bf4cbe05534b.json", "name": "toolcalbuild", "projectIndex": 73}, {"directoryIndex": 75, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-RelWithDebInfo-c63653a6159bc6e8fc34.json", "name": "toolcaltest", "projectIndex": 75}, {"directoryIndex": 74, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-RelWithDebInfo-b441a7fddbb989be3b3c.json", "name": "toolcameraCalibrator", "projectIndex": 74}, {"directoryIndex": 76, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-RelWithDebInfo-84239e414ed4c46e080c.json", "name": "toolcommunication", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-RelWithDebInfo-1c9b38b81aad4ddaf3dc.json", "name": "toolhandeyecal", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-RelWithDebInfo-230fd4b12d12708e2e03.json", "name": "toolhandeyecaltest", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-RelWithDebInfo-3d73fc3e72118e6b177d.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 79}, {"directoryIndex": 80, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-RelWithDebInfo-90c13df06a7f1b1fc616.json", "name": "toolhandeyecaluipath", "projectIndex": 80}, {"directoryIndex": 81, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-RelWithDebInfo-0112df9f7f118f372abe.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 81}, {"directoryIndex": 82, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-RelWithDebInfo-73da8f6856971d39a986.json", "name": "toolverify_calibration", "projectIndex": 82}]}], "kind": "codemodel", "paths": {"build": "D:/newfuxios/build", "source": "D:/newfuxios"}, "version": {"major": 2, "minor": 7}}