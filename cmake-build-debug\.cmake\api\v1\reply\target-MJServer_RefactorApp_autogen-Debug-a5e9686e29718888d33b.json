{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["MJServer_Refactor/App/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"backtrace": 0, "id": "MJServer_RefactorLibrary::@8670365571700e12b583"}, {"backtrace": 0, "id": "fuxicommon::@58335e9a86196d0a97e7"}], "id": "MJServer_RefactorApp_autogen::@f8e8ceb61b4f8c6d7034", "isGeneratorProvided": true, "name": "MJServer_RefactorApp_autogen", "paths": {"build": "MJServer_Refactor/App", "source": "MJServer_Refactor/App"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/MJServer_Refactor/App/CMakeFiles/MJServer_RefactorApp_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/MJServer_Refactor/App/CMakeFiles/MJServer_RefactorApp_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}