{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/Release/Testtest_serial.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/Release/Testtest_serial.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "include_directories"], "files": ["builder/cmake/executable.cmake", "Test/test_serial/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicore/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 73, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 6}, {"file": 3, "parent": 7}, {"command": 3, "file": 3, "line": 78, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 9}, {"command": 1, "file": 3, "line": 1, "parent": 8}, {"file": 2, "parent": 11}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 9, "parent": 13}, {"command": 5, "file": 9, "line": 33, "parent": 14}, {"file": 8, "parent": 15}, {"command": 5, "file": 8, "line": 610, "parent": 16}, {"file": 7, "parent": 17}, {"command": 6, "file": 7, "line": 262, "parent": 18}, {"command": 5, "file": 7, "line": 141, "parent": 19}, {"file": 6, "parent": 20}, {"command": 1, "file": 6, "line": 53, "parent": 21}, {"file": 5, "parent": 22}, {"command": 4, "file": 5, "line": 101, "parent": 23}, {"command": 4, "file": 6, "line": 103, "parent": 21}, {"command": 6, "file": 7, "line": 262, "parent": 18}, {"command": 5, "file": 7, "line": 141, "parent": 26}, {"file": 10, "parent": 27}, {"command": 4, "file": 10, "line": 103, "parent": 28}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 30}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 32}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 34}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 36}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 40}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 42}, {"command": 7, "file": 2, "line": 54, "parent": 43}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 44, "path": "D:/newfuxios/Test/test_serial/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicore/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "C:/opt/glog/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSerialPort"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd"}], "id": "Testtest_serial::@a384ba46c8f7385844c3", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "..\\..\\fuxicore\\Release\\fuxicore.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 25, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 37, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Network_conda.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5SerialPort_conda.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Testtest_serial", "nameOnDisk": "Testtest_serial.exe", "paths": {"build": "Test/test_serial", "source": "Test/test_serial"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_serial/src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}