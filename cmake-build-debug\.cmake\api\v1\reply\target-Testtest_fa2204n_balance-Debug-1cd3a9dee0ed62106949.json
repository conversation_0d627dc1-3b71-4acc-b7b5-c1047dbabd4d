{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/Testtest_fa2204n_balance.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/Testtest_fa2204n_balance.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "include_directories"], "files": ["builder/cmake/executable.cmake", "Test/test_fa2204n_balance/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_hardwaredriverFA2204NBalance.cmake", "builder/cmake/add_libmodbus.cmake", "builder/cmake/add_glog.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 73, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 6}, {"command": 4, "file": 2, "line": 54, "parent": 7}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 3, "parent": 9}, {"command": 4, "file": 3, "line": 10, "parent": 10}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 4, "parent": 12}, {"command": 4, "file": 4, "line": 8, "parent": 13}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 5, "parent": 15}, {"command": 4, "file": 5, "line": 16, "parent": 16}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "includes": [{"backtrace": 8, "path": "D:/newfuxios/Test/test_fa2204n_balance/include"}, {"backtrace": 11, "path": "D:/newfuxios/hardwaredriver/FA2204NBalance/include"}, {"backtrace": 14, "path": "C:/opt/libmodbus/include"}, {"backtrace": 17, "path": "C:/opt/glog/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                                                                                                                                                             /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\libmodbus\\lib\\modbus.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "hardwaredriverFA2204NBalance.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Testtest_fa2204n_balance", "nameOnDisk": "Testtest_fa2204n_balance.exe", "paths": {"build": "Test/test_fa2204n_balance", "source": "Test/test_fa2204n_balance"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_fa2204n_balance/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_fa2204n_balance/src/simple_test.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_fa2204n_balance/src/test_basic.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}