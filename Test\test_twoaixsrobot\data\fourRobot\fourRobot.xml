<?xml version="1.0" encoding="UTF-8"?>
<WorkCell name="FourAxisRobot">


  <SerialDevice name="FourAxisRobot">
    


    <!-- Base frame - Long base -->
    <Frame name="Base">
      <RPY>0 0 0</RPY>
      <Pos>0 0 0</Pos>
    </Frame>
    
	
	<Joint name="J1" type="Prismatic">
      <RPY>0 0 0</RPY>
      <Pos>0 0 0</Pos>
      <PosLimit min="0" max="0.3" />
      <VelLimit max="0.5" />
      <AccLimit max="2.0" />
    </Joint>


    <Joint name="J2" type="Prismatic">
      <RPY>0 0 90</RPY>
      <Pos>0 0 0</Pos>
      <PosLimit min="-0.2" max="0" />
      <VelLimit max="0.5" />
      <AccLimit max="2.0" />
    </Joint>



    <Joint name="J3" type="Revolute">
      <RPY>0 90 0</RPY>
      <Pos>0.220 0.185 -0.108</Pos>
      <PosLimit min="-180" max="0" />
      <VelLimit max="180" />
      <AccLimit max="360" />
    </Joint>

	<Joint name="J4" type="Revolute">
      <RPY>0 90 0</RPY>
      <Pos>0.0 0.0 0</Pos>
      <PosLimit min="-360" max="360" />
      <VelLimit max="180" />
      <AccLimit max="360" />
    </Joint>
    
    <!-- End effector frame -->
    <Frame name="TCP" type="EndEffector">
      <RPY>0 0 0</RPY>
      <Pos>0 0 0.140</Pos>
    </Frame>
    
    <!-- Geometry definitions -->
    <!-- Long base geometry -->
    <Drawable name="BaseGeometry" refframe="Base">
      <RPY>0 0 90</RPY>
      <Pos>0 0 0.050</Pos>
      <Polytope file="geometry/base" />
    </Drawable>



    
    
    <!-- Sliding block geometry -->
    <Drawable name="j1Geometry" refframe="J1">
      <RPY>0 0 90</RPY>
      <Pos>0 0.185 0</Pos>
      <Polytope file="geometry/j1" />
    </Drawable>
    
    <!-- Sliding block geometry -->
    <Drawable name="j2Geometry" refframe="J2">
      <RPY>0 0 0</RPY>
      <Pos>-0.075 0 -0.15</Pos>
      <Polytope file="geometry/j2" />
    </Drawable>
	
	    <!-- Sliding block geometry -->
    <Drawable name="j3Geometry" refframe="J3">
      <RPY>0 -90 17</RPY>
      <Pos>0.13 -0.205 -0.11</Pos>
      <Polytope file="geometry/j3" />
    </Drawable>
	
	    <!-- Sliding block geometry -->
    <Drawable name="j4Geometry" refframe="J4">
      <RPY>0 180 17</RPY>
      <Pos>0.023 -0.176 0.20</Pos>
      <Polytope file="geometry/j4" />
    </Drawable>
    
	<CollisionSetup file="CollisionSetup.xml" />
    
    <!-- Default configurations -->
    <Q name="Home">0 0 -3.14159 0</Q>
  </SerialDevice>


  <Frame name="m_box" refframe="WORLD" type="Movable" daf="true">
     <RPY>0 0 90</RPY>
      <Pos>0.140 0.370 0.328</Pos>
  <Drawable name="beaker" refframe="m_box">
  	<Pos>0 0 0</Pos>
  	<RPY>0 0 0</RPY>
    <Polytope file="geometry/beaker" />
  </Drawable>
  </Frame>
</WorkCell>
