#pragma once

#include "ImageProcessor.h"
#include <vector>
#include <memory>

namespace ABBDraw {

/**
 * @brief 轨迹优化类，用于解决TSP问题和路径优化
 */
class TrajectoryOptimizer {
public:
    using Point2D = ImageProcessor::Point2D;

    /**
     * @brief 构造函数
     */
    TrajectoryOptimizer();

    /**
     * @brief 析构函数
     */
    ~TrajectoryOptimizer();

    /**
     * @brief 优化轨迹路径
     * @param points 输入点集
     * @return 优化后的路径索引序列
     */
    std::vector<int> optimizePath(const std::vector<Point2D>& points);

    /**
     * @brief 计算两点间距离
     * @param p1 点1
     * @param p2 点2
     * @return 欧几里得距离
     */
    static double calculateDistance(const Point2D& p1, const Point2D& p2);

    /**
     * @brief 设置距离阈值（用于判断是否需要抬笔）
     * @param threshold 距离阈值
     */
    void setDistanceThreshold(double threshold) { distanceThreshold_ = threshold; }

    /**
     * @brief 获取距离阈值
     */
    double getDistanceThreshold() const { return distanceThreshold_; }

private:
    double distanceThreshold_;

    /**
     * @brief 计算距离矩阵
     * @param points 点集
     * @return 距离矩阵
     */
    std::vector<std::vector<double>> calculateDistanceMatrix(const std::vector<Point2D>& points);

    /**
     * @brief 贪心算法求解TSP
     * @param distanceMatrix 距离矩阵
     * @return 路径索引序列
     */
    std::vector<int> solveTSPGreedy(const std::vector<std::vector<double>>& distanceMatrix);

    /**
     * @brief 最近邻算法
     * @param distanceMatrix 距离矩阵
     * @param startIndex 起始点索引
     * @return 路径索引序列
     */
    std::vector<int> nearestNeighborTSP(const std::vector<std::vector<double>>& distanceMatrix, int startIndex = 0);
};

} // namespace ABBDraw