#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>

namespace ABBDraw {

/**
 * @brief 图像处理类，用于边缘检测和轮廓提取
 */
class ImageProcessor {
public:
    struct Point2D {
        double x, y;
        Point2D(double x = 0, double y = 0) : x(x), y(y) {}
    };

    /**
     * @brief 构造函数
     */
    ImageProcessor();

    /**
     * @brief 析构函数
     */
    ~ImageProcessor();

    /**
     * @brief 处理图像，提取边缘点
     * @param imagePath 图像文件路径
     * @param scaleFactor 缩放因子
     * @param targetSize 目标尺寸
     * @return 边缘点列表
     */
    std::vector<Point2D> processImage(const std::string& imagePath,
                                     double scaleFactor = 1.0,
                                     int targetSize = 350);

    /**
     * @brief 获取处理后的图像尺寸
     */
    cv::Size getProcessedImageSize() const { return processedSize_; }

    /**
     * @brief 显示处理结果
     */
    void showResult() const;

private:
    cv::Mat originalImage_;
    cv::Mat processedImage_;
    cv::Mat edgeImage_;
    cv::Size processedSize_;

    /**
     * @brief 调整图像尺寸
     */
    cv::Mat resizeImage(const cv::Mat& image, int targetSize);

    /**
     * @brief 边缘检测
     */
    cv::Mat detectEdges(const cv::Mat& grayImage);

    /**
     * @brief 从边缘图像提取点
     */
    std::vector<Point2D> extractPoints(const cv::Mat& edgeImage, double scaleFactor);
};

} // namespace ABBDraw