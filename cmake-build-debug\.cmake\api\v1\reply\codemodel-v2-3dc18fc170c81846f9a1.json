{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": "."}, {"build": "ABBDrawPicture", "hasInstallRule": true, "jsonFile": "directory-ABBDrawPicture-Debug-21e5a8551edda0d50307.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "ABBDrawPicture", "targetIndexes": [0, 1]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-Debug-b79a1355447c6330d885.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "fuxicommon", "targetIndexes": [72, 73]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-Debug-90f4d5bc949ff5dbc804.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [4]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-Debug-0d036c006d6835e1ad64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [6]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-Debug-c121f1d6e3a37c026ae5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [10]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-Debug-c812c39218a98b3b4900.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [7]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-Debug-3c6bc42d439b6c09ccb1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [8]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-Debug-e3840333b771d7546807.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [9]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-Debug-88caefc30fc1e326840a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [12]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-Debug-11a5140d3bb0dc9a8273.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [11]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-Debug-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/App", "targetIndexes": [2]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-Debug-d43460d509b2a2170548.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [3]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-Debug-ac3b6fec17b5d24d4c2d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [5]}, {"build": "Analysis_Robot/test/balanceDriverTest", "jsonFile": "directory-Analysis_Robot.test.balanceDriverTest-Debug-7e4f5522755cfdc4e01f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/balanceDriverTest", "targetIndexes": [13]}, {"build": "Analysis_Robot/test/balanceTest", "jsonFile": "directory-Analysis_Robot.test.balanceTest-Debug-4741c58a4b5c91f9121e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "Analysis_Robot/test/balanceTest", "targetIndexes": [14]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-Debug-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [15]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-Debug-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [16]}, {"build": "Analysis_Robot/test/moistureAnalyzerDriverTest", "jsonFile": "directory-Analysis_Robot.test.moistureAnalyzerDriverTest-Debug-6df43ddc17b8d29e6a25.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "Analysis_Robot/test/moistureAnalyzerDriverTest", "targetIndexes": [17]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-Debug-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer/APP", "targetIndexes": [18, 19]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-Debug-9b728c189064dbe0a67c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/Library", "targetIndexes": [22, 23]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-Debug-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "MJServer_Refactor/App", "targetIndexes": [20, 21]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-Debug-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [24, 25]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-Debug-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [26, 27]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-Debug-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [28, 29]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-Debug-e1ff6c16bfa1adaa139e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [30]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-Debug-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [31, 32]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-Debug-ae506fbd60f92fd6663c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [33]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-Debug-164723f82aafbdb585d0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [46]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-Debug-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [34, 35]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-Debug-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [36, 39]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-Debug-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [37, 38]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-Debug-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [40, 41]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-Debug-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [42, 43]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-Debug-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "RoboticLaserMarking/UI", "targetIndexes": [44, 45]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-Debug-156a86953dd61d902f61.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [47]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-Debug-498b3b68499ddf8b9f89.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "fuxicore", "targetIndexes": [74, 75]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-Debug-0f25266438a8c96a92f6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [92, 93]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-Debug-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_abb_socket", "targetIndexes": [48, 49]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-Debug-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_config_manager", "targetIndexes": [50]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-Debug-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_csv", "targetIndexes": [51]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-Debug-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_event_listener", "targetIndexes": [52]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-Debug-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_executor", "targetIndexes": [53]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-Debug-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_executor_context", "targetIndexes": [54]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-Debug-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_fa2204n_balance", "targetIndexes": [55]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-Debug-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [56]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-Debug-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_fileutil", "targetIndexes": [57]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-Debug-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_json", "targetIndexes": [58]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-Debug-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_license_manager", "targetIndexes": [59, 60]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-Debug-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_license_ui", "targetIndexes": [61, 62]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-Debug-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_micro_dosing", "targetIndexes": [105]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-Debug-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_network", "targetIndexes": [63]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-Debug-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_serial", "targetIndexes": [64]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-Debug-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_service_container", "targetIndexes": [65]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-Debug-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_socket", "targetIndexes": [66]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-Debug-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "Test/test_sqlite", "targetIndexes": [67]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-Debug-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "Test/test_taskflow", "targetIndexes": [68]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-Debug-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "Test/test_twoaixsrobot", "targetIndexes": [69, 70]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-Debug-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "Test/test_xml", "targetIndexes": [71]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-Debug-c45e0ac386a4c7b9645c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [76, 77]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-Debug-ebe07896589935f07b2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/AuboDriver", "targetIndexes": [78, 79]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-Debug-821b0d83718bc6e2ed7b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [80, 81]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-Debug-4b773c429dc45089025b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [82, 83]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-Debug-fbec16a6fc271f8dd9a7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [84, 85]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-Debug-66019e4878385d088647.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [86, 87]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-Debug-85f2e29849d682a5abbb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/OpcDa", "targetIndexes": [88, 89]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-Debug-35b736c6f4696724ce93.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/OpcUa", "targetIndexes": [90, 91]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-Debug-53343f20d19b315418df.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/socket", "targetIndexes": [102, 103]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-Debug-2956612909533ed5f553.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [94, 95]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-Debug-a6ba1a2cf998a075dc08.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [96]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-Debug-8d705ae629216097414a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [97]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-Debug-1dcdc9a49ca90396ce27.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "hardwaredriver/modbus", "targetIndexes": [98, 99]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-Debug-03888b08cf4ca132fb87.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "hardwaredriver/serial", "targetIndexes": [100, 101]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-Debug-7b48558d41c2b39deef3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "hardwaredriver/usbcamera", "targetIndexes": [104]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-Debug-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/calbuild", "targetIndexes": [106]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-Debug-4d49ebeed26da0de41ca.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/cameraCalibrator", "targetIndexes": [108]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-Debug-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/caltest", "targetIndexes": [107]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-Debug-3c0082eedfb835a430aa.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/communication", "targetIndexes": [109, 110]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-Debug-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecal", "targetIndexes": [111]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-Debug-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/handeyecaltest", "targetIndexes": [112]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-Debug-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 80, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [113, 114]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-Debug-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 81, "source": "tool/handeyecaluipath", "targetIndexes": [115, 118]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-Debug-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 82, "source": "tool/handeyecaluipathAuto", "targetIndexes": [116, 117]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-Debug-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 83, "source": "tool/verify_calibration", "targetIndexes": [119]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83], "directoryIndexes": [0], "name": "Project"}, {"directoryIndexes": [1], "name": "ABBDrawPicture", "parentIndex": 0, "targetIndexes": [0, 1]}, {"directoryIndexes": [2], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [72, 73]}, {"directoryIndexes": [3], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [4]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [6]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [10]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [7]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [8]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [9]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [12]}, {"directoryIndexes": [10], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [11]}, {"directoryIndexes": [11], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [3]}, {"directoryIndexes": [13], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [5]}, {"directoryIndexes": [14], "name": "Analysis_RobottestbalanceDriverTest", "parentIndex": 0, "targetIndexes": [13]}, {"directoryIndexes": [15], "name": "Analysis_RobottestbalanceTest", "parentIndex": 0, "targetIndexes": [14]}, {"directoryIndexes": [16], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [15]}, {"directoryIndexes": [17], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [16]}, {"directoryIndexes": [18], "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "parentIndex": 0, "targetIndexes": [17]}, {"directoryIndexes": [19], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [18, 19]}, {"directoryIndexes": [20], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [22, 23]}, {"directoryIndexes": [21], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [20, 21]}, {"directoryIndexes": [22], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [24, 25]}, {"directoryIndexes": [23], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [26, 27]}, {"directoryIndexes": [24], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [28, 29]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [30]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [31, 32]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [33]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [46]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [34, 35]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [36, 39]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [37, 38]}, {"directoryIndexes": [32], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [40, 41]}, {"directoryIndexes": [33], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [42, 43]}, {"directoryIndexes": [34], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [44, 45]}, {"directoryIndexes": [35], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [47]}, {"directoryIndexes": [36], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [74, 75]}, {"directoryIndexes": [37], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [92, 93]}, {"directoryIndexes": [38], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [48, 49]}, {"directoryIndexes": [39], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [50]}, {"directoryIndexes": [40], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [51]}, {"directoryIndexes": [41], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [52]}, {"directoryIndexes": [42], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [53]}, {"directoryIndexes": [43], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [54]}, {"directoryIndexes": [44], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [55]}, {"directoryIndexes": [45], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [56]}, {"directoryIndexes": [46], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [57]}, {"directoryIndexes": [47], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [58]}, {"directoryIndexes": [48], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [59, 60]}, {"directoryIndexes": [49], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [61, 62]}, {"directoryIndexes": [50], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [105]}, {"directoryIndexes": [51], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [63]}, {"directoryIndexes": [52], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [64]}, {"directoryIndexes": [53], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [65]}, {"directoryIndexes": [54], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [66]}, {"directoryIndexes": [55], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [67]}, {"directoryIndexes": [56], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [68]}, {"directoryIndexes": [57], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [69, 70]}, {"directoryIndexes": [58], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [71]}, {"directoryIndexes": [59], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [76, 77]}, {"directoryIndexes": [60], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [78, 79]}, {"directoryIndexes": [61], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [80, 81]}, {"directoryIndexes": [62], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [82, 83]}, {"directoryIndexes": [63], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [84, 85]}, {"directoryIndexes": [64], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [86, 87]}, {"directoryIndexes": [65], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [88, 89]}, {"directoryIndexes": [66], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [90, 91]}, {"directoryIndexes": [67], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [102, 103]}, {"directoryIndexes": [68], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [94, 95]}, {"directoryIndexes": [69], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [96]}, {"directoryIndexes": [70], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [97]}, {"directoryIndexes": [71], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [98, 99]}, {"directoryIndexes": [72], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [100, 101]}, {"directoryIndexes": [73], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [104]}, {"directoryIndexes": [74], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [106]}, {"directoryIndexes": [75], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [108]}, {"directoryIndexes": [76], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [107]}, {"directoryIndexes": [77], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [109, 110]}, {"directoryIndexes": [78], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [111]}, {"directoryIndexes": [79], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [112]}, {"directoryIndexes": [80], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [113, 114]}, {"directoryIndexes": [81], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [115, 118]}, {"directoryIndexes": [82], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [116, 117]}, {"directoryIndexes": [83], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [119]}], "targets": [{"directoryIndex": 1, "id": "ABBDrawPicture::@88e10c1018c6b9f34ba8", "jsonFile": "target-ABBDrawPicture-Debug-6f36a7ccdd3543f6844b.json", "name": "ABBDrawPicture", "projectIndex": 1}, {"directoryIndex": 1, "id": "ABBDrawPicture_autogen::@88e10c1018c6b9f34ba8", "jsonFile": "target-ABBDrawPicture_autogen-Debug-5b2c26c707550e1c3af6.json", "name": "ABBDrawPicture_autogen", "projectIndex": 1}, {"directoryIndex": 11, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-Debug-77da47074a7dc037d270.json", "name": "Analysis_RobotApp", "projectIndex": 11}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-Debug-92970e99fd258f5f4306.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-Debug-785fe97389eb1d5b3a9e.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 3}, {"directoryIndex": 13, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-Debug-c74b0e4072ade0440bbe.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 13}, {"directoryIndex": 4, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-Debug-c56f4b844390f9840849.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 4}, {"directoryIndex": 6, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-Debug-3fe520d446f529178553.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-Debug-1e3025d8df0e43a933b7.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 7}, {"directoryIndex": 8, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-Debug-12bf8f536994bddcd8f3.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 8}, {"directoryIndex": 5, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-Debug-99e01f6885a1cb3b6419.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 5}, {"directoryIndex": 10, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-Debug-3f44bfd04b2a73cca98b.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 10}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-Debug-d32104cfcd679d53b840.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 9}, {"directoryIndex": 14, "id": "Analysis_RobottestbalanceDriverTest::@f2032f6c36bb657d8ab6", "jsonFile": "target-Analysis_RobottestbalanceDriverTest-Debug-e2ae1b980d561349c6f9.json", "name": "Analysis_RobottestbalanceDriverTest", "projectIndex": 14}, {"directoryIndex": 15, "id": "Analysis_RobottestbalanceTest::@67fcef0db64755a7efa1", "jsonFile": "target-Analysis_RobottestbalanceTest-Debug-a4b0761ba82d14966107.json", "name": "Analysis_RobottestbalanceTest", "projectIndex": 15}, {"directoryIndex": 16, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-Debug-8c2b727be324aa20d141.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 16}, {"directoryIndex": 17, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-Debug-c568d0225c283f512ead.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 17}, {"directoryIndex": 18, "id": "Analysis_RobottestmoistureAnalyzerDriverTest::@344f5fe5f7bc7b70cade", "jsonFile": "target-Analysis_RobottestmoistureAnalyzerDriverTest-Debug-a39fe5a49dcf13c7bfb4.json", "name": "Analysis_RobottestmoistureAnalyzerDriverTest", "projectIndex": 18}, {"directoryIndex": 19, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-Debug-f448eda47c89d4fd408b.json", "name": "MJServerAPP", "projectIndex": 19}, {"directoryIndex": 19, "id": "MJServerAPP_autogen::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP_autogen-Debug-d90fc0c04292e41212fe.json", "name": "MJServerAPP_autogen", "projectIndex": 19}, {"directoryIndex": 21, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-Debug-1e68158daeb1f7e8463c.json", "name": "MJServer_RefactorApp", "projectIndex": 21}, {"directoryIndex": 21, "id": "MJServer_RefactorApp_autogen::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp_autogen-Debug-a5e9686e29718888d33b.json", "name": "MJServer_RefactorApp_autogen", "projectIndex": 21}, {"directoryIndex": 20, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-Debug-a848d171ac38ff368361.json", "name": "MJServer_RefactorLibrary", "projectIndex": 20}, {"directoryIndex": 20, "id": "MJServer_RefactorLibrary_autogen::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary_autogen-Debug-0ec46314a21ae102334b.json", "name": "MJServer_RefactorLibrary_autogen", "projectIndex": 20}, {"directoryIndex": 22, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-Debug-502f763c1b14ef89e23c.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 22}, {"directoryIndex": 22, "id": "MJServer_RefactorTestphase1_test_autogen::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test_autogen-Debug-69462032026aeb8414db.json", "name": "MJServer_RefactorTestphase1_test_autogen", "projectIndex": 22}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-Debug-787d06822acbfbc0f69e.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 23}, {"directoryIndex": 23, "id": "MJServer_RefactorTestsimple_abb_client_autogen::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client_autogen-Debug-2ba7d3d8f4cf75943397.json", "name": "MJServer_RefactorTestsimple_abb_client_autogen", "projectIndex": 23}, {"directoryIndex": 24, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-Debug-b79636965d06a7391cb5.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 24}, {"directoryIndex": 24, "id": "MJServer_RefactorTestsimple_feeder_client_autogen::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client_autogen-Debug-e3269e964e4567d9b744.json", "name": "MJServer_RefactorTestsimple_feeder_client_autogen", "projectIndex": 24}, {"directoryIndex": 25, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-Debug-a124195ac577f84b0991.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-Debug-05ea61fa9e4884afb53c.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 26}, {"directoryIndex": 26, "id": "RoboticLaserMarkingLicenseGenerator_autogen::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator_autogen-Debug-0640702f4a5faa08763c.json", "name": "RoboticLaserMarkingLicenseGenerator_autogen", "projectIndex": 26}, {"directoryIndex": 27, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-Debug-70f6dce204913492cce5.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 27}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-Debug-1ef845a2623e135108f2.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 29}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestabbsocket_autogen::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket_autogen-Debug-f24c38dcb5cb50fc7b71.json", "name": "RoboticLaserMarkingTestabbsocket_autogen", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-Debug-bc09dcf2fa3ff8ad1d4b.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 30}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-Debug-96322ac630ac20a5218b.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 31}, {"directoryIndex": 31, "id": "RoboticLaserMarkingTestlaserUI_autogen::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI_autogen-Debug-3bf682460f3769a4d4fc.json", "name": "RoboticLaserMarkingTestlaserUI_autogen", "projectIndex": 31}, {"directoryIndex": 30, "id": "RoboticLaserMarkingTestlaser_autogen::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser_autogen-Debug-0c686d6b75080d4c403d.json", "name": "RoboticLaserMarkingTestlaser_autogen", "projectIndex": 30}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-Debug-2af49dd9c04368146025.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 32}, {"directoryIndex": 32, "id": "RoboticLaserMarkingTestrfiddriver_autogen::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver_autogen-Debug-fb08d148b5e84c925f46.json", "name": "RoboticLaserMarkingTestrfiddriver_autogen", "projectIndex": 32}, {"directoryIndex": 33, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-Debug-0215ee8505b81d559be1.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 33}, {"directoryIndex": 33, "id": "RoboticLaserMarkingTestrfidserver_autogen::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver_autogen-Debug-de4d217149b8a4f8a018.json", "name": "RoboticLaserMarkingTestrfidserver_autogen", "projectIndex": 33}, {"directoryIndex": 34, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-Debug-4548e8d518de87fe0496.json", "name": "RoboticLaserMarkingUI", "projectIndex": 34}, {"directoryIndex": 34, "id": "RoboticLaserMarkingUI_autogen::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI_autogen-Debug-dc328c035c4f6b2a8e49.json", "name": "RoboticLaserMarkingUI_autogen", "projectIndex": 34}, {"directoryIndex": 28, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-Debug-56223c24225e0ee7c10e.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 28}, {"directoryIndex": 35, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-Debug-232de827d5498c8d4686.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 35}, {"directoryIndex": 38, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-Debug-7aa795779ba6729fdc91.json", "name": "Testtest_abb_socket", "projectIndex": 38}, {"directoryIndex": 38, "id": "Testtest_abb_socket_autogen::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket_autogen-Debug-336b85015424773b9982.json", "name": "Testtest_abb_socket_autogen", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-Debug-09e6cbe1562442a80451.json", "name": "Testtest_config_manager", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-Debug-57de7be256c868531591.json", "name": "Testtest_csv", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-Debug-2e2bf639e3aac92ba48b.json", "name": "Testtest_event_listener", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-Debug-5810340e55e0acaee442.json", "name": "Testtest_executor", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-Debug-0c69e3fc1f88a300c9e9.json", "name": "Testtest_executor_context", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-Debug-1cd3a9dee0ed62106949.json", "name": "Testtest_fa2204n_balance", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-Debug-6067b23290b105dedfac.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 45}, {"directoryIndex": 46, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-Debug-9c37bc89101b1ef0bf47.json", "name": "Testtest_fileutil", "projectIndex": 46}, {"directoryIndex": 47, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-Debug-184017d06bced23a8c54.json", "name": "Testtest_json", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-Debug-7b8df2f2baa11d4da185.json", "name": "Testtest_license_manager", "projectIndex": 48}, {"directoryIndex": 48, "id": "Testtest_license_manager_autogen::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager_autogen-Debug-8d6131616e77a77a3f67.json", "name": "Testtest_license_manager_autogen", "projectIndex": 48}, {"directoryIndex": 49, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-Debug-d302959362a751d2c11f.json", "name": "Testtest_license_ui", "projectIndex": 49}, {"directoryIndex": 49, "id": "Testtest_license_ui_autogen::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui_autogen-Debug-e087808480b63a364c6c.json", "name": "Testtest_license_ui_autogen", "projectIndex": 49}, {"directoryIndex": 51, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-Debug-bfda376c02295b1330e2.json", "name": "Testtest_network", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-Debug-87796552ebeb63239a87.json", "name": "Testtest_serial", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-Debug-589fe51340c41326257e.json", "name": "Testtest_service_container", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-Debug-6b18cf83fd41de70a228.json", "name": "Testtest_socket", "projectIndex": 54}, {"directoryIndex": 55, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-Debug-3510dffdb475988cb10c.json", "name": "Testtest_sqlite", "projectIndex": 55}, {"directoryIndex": 56, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-Debug-0dcf69f2de74cc989842.json", "name": "Testtest_taskflow", "projectIndex": 56}, {"directoryIndex": 57, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-Debug-b3056b2078086f945816.json", "name": "Testtest_twoaixsrobot", "projectIndex": 57}, {"directoryIndex": 57, "id": "Testtest_twoaixsrobot_autogen::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot_autogen-Debug-49e69329bb729ce80483.json", "name": "Testtest_twoaixsrobot_autogen", "projectIndex": 57}, {"directoryIndex": 58, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-Debug-54cbdd230872681c26bd.json", "name": "Testtest_xml", "projectIndex": 58}, {"directoryIndex": 2, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-Debug-8cd6a77cfd41cb6f5a95.json", "name": "fuxicommon", "projectIndex": 2}, {"directoryIndex": 2, "id": "fuxicommon_autogen::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon_autogen-Debug-c633b73168c7d64932be.json", "name": "fuxicommon_autogen", "projectIndex": 2}, {"directoryIndex": 36, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-Debug-36d8a940c19a21e5a6f1.json", "name": "fuxicore", "projectIndex": 36}, {"directoryIndex": 36, "id": "fuxicore_autogen::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore_autogen-Debug-dd35c434570d490e72aa.json", "name": "fuxicore_autogen", "projectIndex": 36}, {"directoryIndex": 59, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-Debug-2509d876f68df02d11c7.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 59}, {"directoryIndex": 59, "id": "hardwaredriverAuboArcsDriver_autogen::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver_autogen-Debug-c805b439f5ffb17f4835.json", "name": "hardwaredriverAuboArcsDriver_autogen", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-Debug-8ed5eb1d710d9019436e.json", "name": "hardwaredriverAuboDriver", "projectIndex": 60}, {"directoryIndex": 60, "id": "hardwaredriverAuboDriver_autogen::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver_autogen-Debug-e435e4788290e4be174a.json", "name": "hardwaredriverAuboDriver_autogen", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-Debug-c53aa0e6a7716be3f5c5.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 61}, {"directoryIndex": 61, "id": "hardwaredriverElectricGripperDriver_autogen::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver_autogen-Debug-decd97aabfff07afb0aa.json", "name": "hardwaredriverElectricGripperDriver_autogen", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-Debug-a5c40b0a25deffe7c0af.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 62}, {"directoryIndex": 62, "id": "hardwaredriverHikVisionCamera_autogen::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera_autogen-Debug-4b66e8a502028e574a4b.json", "name": "hardwaredriverHikVisionCamera_autogen", "projectIndex": 62}, {"directoryIndex": 63, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-Debug-16c320a451d9b9ad4a7d.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 63}, {"directoryIndex": 63, "id": "hardwaredriverLabelPrinter_autogen::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter_autogen-Debug-b31f7c230cd251bd9495.json", "name": "hardwaredriverLabelPrinter_autogen", "projectIndex": 63}, {"directoryIndex": 64, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-Debug-d1d2e77e5c622f72531b.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 64}, {"directoryIndex": 64, "id": "hardwaredriverMettlerBalance_autogen::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance_autogen-Debug-098284c751fdefce26dd.json", "name": "hardwaredriverMettlerBalance_autogen", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-Debug-dbc3375ed216c582dc1b.json", "name": "hardwaredriverOpcDa", "projectIndex": 65}, {"directoryIndex": 65, "id": "hardwaredriverOpcDa_autogen::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa_autogen-Debug-4130421335d8e917c138.json", "name": "hardwaredriverOpcDa_autogen", "projectIndex": 65}, {"directoryIndex": 66, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-Debug-97b24f057a79fd15f44f.json", "name": "hardwaredriverOpcUa", "projectIndex": 66}, {"directoryIndex": 66, "id": "hardwaredriverOpcUa_autogen::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa_autogen-Debug-bc2926d23d7fbcfdadf5.json", "name": "hardwaredriverOpcUa_autogen", "projectIndex": 66}, {"directoryIndex": 37, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-Debug-848b429f46a62d0391bc.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 37}, {"directoryIndex": 37, "id": "hardwaredriverabbRobotDriver_autogen::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver_autogen-Debug-a6833890493d98c9615c.json", "name": "hardwaredriverabbRobotDriver_autogen", "projectIndex": 37}, {"directoryIndex": 68, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-Debug-f1f2549a3621987f3501.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 68}, {"directoryIndex": 68, "id": "hardwaredriveragilerobotDriver_autogen::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver_autogen-Debug-f664c35dea0d58e0703a.json", "name": "hardwaredriveragilerobotDriver_autogen", "projectIndex": 68}, {"directoryIndex": 69, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-Debug-f9d0666a8c5f3da5664e.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 69}, {"directoryIndex": 70, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-Debug-c013b4790c84ec6e23bd.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 70}, {"directoryIndex": 71, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-Debug-e6e638ccb272a07e2890.json", "name": "hardwaredrivermodbus", "projectIndex": 71}, {"directoryIndex": 71, "id": "hardwaredrivermodbus_autogen::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus_autogen-Debug-019d217487578ed5e61e.json", "name": "hardwaredrivermodbus_autogen", "projectIndex": 71}, {"directoryIndex": 72, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-Debug-e2ce9d7dc8d5e41fe8b6.json", "name": "hardwaredriverserial", "projectIndex": 72}, {"directoryIndex": 72, "id": "hardwaredriverserial_autogen::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial_autogen-Debug-367054d93197e5bf2300.json", "name": "hardwaredriverserial_autogen", "projectIndex": 72}, {"directoryIndex": 67, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-Debug-8ea4f1af0a23ac836e70.json", "name": "hardwaredriversocket", "projectIndex": 67}, {"directoryIndex": 67, "id": "hardwaredriversocket_autogen::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket_autogen-Debug-25a08b1363fdaa711a74.json", "name": "hardwaredriversocket_autogen", "projectIndex": 67}, {"directoryIndex": 73, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-Debug-0d86ba2cec15e8951544.json", "name": "hardwaredriverusbcamera", "projectIndex": 73}, {"directoryIndex": 50, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-Debug-7cec31a85bdb74fc6aa4.json", "name": "test_micro_dosing", "projectIndex": 50}, {"directoryIndex": 74, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-Debug-002e33d76470b0eb3a4e.json", "name": "toolcalbuild", "projectIndex": 74}, {"directoryIndex": 76, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-Debug-b07595d907270df76869.json", "name": "toolcaltest", "projectIndex": 76}, {"directoryIndex": 75, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-Debug-ab1b5867fc90da1b1864.json", "name": "toolcameraCalibrator", "projectIndex": 75}, {"directoryIndex": 77, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-Debug-1184ef5c0684ec0606ca.json", "name": "toolcommunication", "projectIndex": 77}, {"directoryIndex": 77, "id": "toolcommunication_autogen::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication_autogen-Debug-ded8444e80c0b3eae7a8.json", "name": "toolcommunication_autogen", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-Debug-7222d181c81770e8c38f.json", "name": "toolhandeyecal", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-Debug-68883c0d64a03b8b780c.json", "name": "toolhandeyecaltest", "projectIndex": 79}, {"directoryIndex": 80, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-Debug-cb5d71c79b8ffcdb1f33.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 80}, {"directoryIndex": 80, "id": "toolhandeyecaluihandeyecalui_autogen::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui_autogen-Debug-d9f1de7b4e08c1131ed3.json", "name": "toolhandeyecaluihandeyecalui_autogen", "projectIndex": 80}, {"directoryIndex": 81, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-Debug-ff88484fb72cecb32f59.json", "name": "toolhandeyecaluipath", "projectIndex": 81}, {"directoryIndex": 82, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-Debug-3f71deb95cbc618297b4.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 82}, {"directoryIndex": 82, "id": "toolhandeyecaluipathAuto_autogen::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto_autogen-Debug-1709578d16c3dfbea6d6.json", "name": "toolhandeyecaluipathAuto_autogen", "projectIndex": 82}, {"directoryIndex": 81, "id": "toolhandeyecaluipath_autogen::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath_autogen-Debug-843b8ac9ba6998d015db.json", "name": "toolhandeyecaluipath_autogen", "projectIndex": 81}, {"directoryIndex": 83, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-Debug-ddcd19477e290c9c1f5f.json", "name": "toolverify_calibration", "projectIndex": 83}]}], "kind": "codemodel", "paths": {"build": "D:/newfuxios/cmake-build-debug", "source": "D:/newfuxios"}, "version": {"major": 2, "minor": 7}}