# ABB机器人绘图系统 (C++)

基于图像处理和轨迹优化的ABB机器人绘图解决方案，适用于大规模定制/个性化产品生产。

## 项目概述

本项目将原始的Python项目转换为C++实现，集成了现有的ABB机器人驱动，提供了完整的图像处理、轨迹优化和机器人控制功能。

### 主要功能

- **图像处理**: 使用OpenCV进行边缘检测和轮廓提取
- **轨迹优化**: 实现TSP问题求解，优化绘图路径
- **机器人控制**: 集成现有的AbbRobotDriver，支持实时机器人控制
- **RAPID代码生成**: 自动生成ABB机器人的RAPID程序代码
- **坐标系转换**: 图像坐标到机器人坐标的自动转换

### 技术特点

- 模块化设计，易于扩展和维护
- 支持任意ABB机器人型号
- 实时图像处理和路径优化
- 安全的机器人运动控制
- 完整的错误处理和日志记录

## 系统架构

```
ABBDrawPicture/
├── include/                 # 头文件
│   ├── ImageProcessor.h     # 图像处理类
│   ├── TrajectoryOptimizer.h # 轨迹优化类
│   └── ABBDrawController.h  # 主控制器类
├── src/                     # 源文件
│   ├── ImageProcessor.cpp
│   ├── TrajectoryOptimizer.cpp
│   └── ABBDrawController.cpp
├── App/                     # 应用程序
│   └── main.cpp            # 主程序入口
├── config.json             # 配置文件
├── CMakeLists.txt          # 构建配置
└── README.md               # 说明文档
```

## 依赖项

- OpenCV (图像处理)
- RobWork (机器人学库)
- 现有的AbbRobotDriver
- Qt5 (界面支持)
- Boost (工具库)

## 编译和安装

### 前提条件

确保已安装以下依赖：
- CMake 3.16+
- OpenCV 4.x
- Qt5
- RobWork
- Visual Studio 2019+ (Windows)

### 编译步骤

1. 克隆项目到本地
2. 在项目根目录下创建build目录
3. 使用CMake配置和编译

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

## 使用方法

### 基本用法

```bash
ABBDrawPicture -i image.jpg -s 1.0 -t 350
```

### 命令行参数

- `-i, --image <路径>`: 输入图像文件路径
- `-s, --scale <因子>`: 缩放因子 (默认: 1.0)
- `-t, --target <尺寸>`: 目标尺寸 (默认: 350)
- `-r, --robot <IP>`: 机器人IP地址 (默认: *************)
- `-p, --port <端口>`: 机器人端口 (默认: 7000)
- `-h, --height <高度>`: 绘图高度 (默认: 0.1065)
- `--help`: 显示帮助信息

### 配置文件

可以通过修改`config.json`文件来调整系统参数：

```json
{
    "robot": {
        "ip": "*************",
        "port": 7000
    },
    "drawing": {
        "speed": 5.0,
        "zone": 1,
        "acceleration": 100
    }
}
```

## 工作流程

1. **图像加载**: 读取输入图像文件
2. **图像预处理**: 调整尺寸、转换为灰度图
3. **边缘检测**: 使用Canny算法提取边缘
4. **点提取**: 从边缘图像中提取绘图点
5. **轨迹优化**: 使用TSP算法优化绘图路径
6. **坐标转换**: 将图像坐标转换为机器人坐标
7. **机器人控制**: 执行绘图动作或生成RAPID代码

## 安全注意事项

⚠️ **重要提醒**:
- 使用真实机器人时请务必小心
- 首次运行建议使用低速度参数
- 确保工作区域安全，无障碍物
- 建议先在仿真环境中测试

## 扩展功能

- 支持多种图像格式 (JPG, PNG, BMP等)
- 可配置的边缘检测参数
- 多种TSP求解算法
- 自定义坐标系标定
- 实时绘图进度显示

## 故障排除

### 常见问题

1. **机器人连接失败**
   - 检查IP地址和端口设置
   - 确认机器人控制器已启动
   - 检查网络连接

2. **图像处理失败**
   - 确认图像文件路径正确
   - 检查图像格式是否支持
   - 调整图像尺寸参数

3. **编译错误**
   - 检查依赖库是否正确安装
   - 确认CMake配置正确
   - 检查编译器版本

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过Issue联系我们。