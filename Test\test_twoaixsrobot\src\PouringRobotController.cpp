﻿#include <rw/rw.hpp>
#include <rw/loaders/WorkCellLoader.hpp>
#include <rw/models/WorkCell.hpp>
#include <rw/models/Device.hpp>
#include <rw/kinematics/State.hpp>
#include <rw/math/Q.hpp>
#include <rw/math/Transform3D.hpp>
#include <rw/math/Vector3D.hpp>
#include <rw/math/RPY.hpp>
#include <iostream>
#include <cmath>
#include <thread>
#include <sstream>
#include <chrono>
#include <math.h>
#include <random>
#include <algorithm>
#include <iomanip>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "httplib.h"

using namespace rw::common;
using namespace rw::loaders;
using namespace rw::models;
using namespace rw::kinematics;
using namespace rw::math;

// PID Controller Class
class PIDController {
private:
    double kp, ki, kd;
    double previousError;
    double integral;
    std::chrono::steady_clock::time_point lastTime;
    bool firstRun;

public:
    PIDController(double p = 1.0, double i = 0.1, double d = 0.05)
        : kp(p), ki(i), kd(d), previousError(0.0), integral(0.0), firstRun(true) {}

    double calculate(double setpoint, double processValue) {
        auto currentTime = std::chrono::steady_clock::now();

        if (firstRun) {
            lastTime = currentTime;
            firstRun = false;
            previousError = setpoint - processValue;
            return 0.0;
        }

        double deltaTime = std::chrono::duration<double>(currentTime - lastTime).count();
        if (deltaTime <= 0.0) deltaTime = 0.001;

        double error = setpoint - processValue;

        // Proportional term
        double proportional = kp * error;

        // Integral term
        integral += error * deltaTime;
        double integralTerm = ki * integral;

        // Derivative term
        double derivative = (error - previousError) / deltaTime;
        double derivativeTerm = kd * derivative;

        // Calculate output
        double output = proportional + integralTerm + derivativeTerm;

        // Update for next iteration
        previousError = error;
        lastTime = currentTime;

        return output;
    }

    void reset() {
        previousError = 0.0;
        integral = 0.0;
        firstRun = true;
    }

    void setParameters(double p, double i, double d) {
        kp = p;
        ki = i;
        kd = d;
    }
};

// Simulated Scale Class
class SimulatedScale {
private:
    double currentWeight;
    double noiseLevel;
    std::mt19937 generator;
    std::normal_distribution<double> noiseDist;

public:
    SimulatedScale(double noise = 0.1)
        : currentWeight(0.0), noiseLevel(noise), generator(std::random_device{}()), noiseDist(0.0, noise) {}

    void addWeight(double weight) {
        currentWeight += weight;
    }

    double getWeight() {
        // Add some noise to simulate real scale behavior
        return currentWeight + noiseDist(generator);
    }

    void reset() {
        currentWeight = 0.0;
    }
};

// Liquid Flow Simulator Class
class LiquidFlowSimulator {
private:
    double containerVolume;      // Total volume in container (ml)
    double currentVolume;        // Current volume in container (ml)
    double liquidDensity;        // Liquid density (g/ml)
    double maxFlowRate;          // Maximum flow rate (ml/s)
    double minPouringAngle;      // Minimum angle to start pouring (degrees)

public:
    LiquidFlowSimulator(double volume = 500.0, double density = 1.0, double maxFlow = 50.0, double minAngle = 10.0)
        : containerVolume(volume), currentVolume(volume), liquidDensity(density),
          maxFlowRate(maxFlow), minPouringAngle(minAngle) {}

    double calculateFlowRate(double pouringAngle) {
        if (pouringAngle < minPouringAngle || currentVolume <= 0.0) {
            return 0.0;
        }

        // 基于物理的流速计算模型 - 角度越大流速越快
        double effectiveAngle = pouringAngle - minPouringAngle;
        double maxEffectiveAngle = 90.0 - minPouringAngle;

        // 确保角度在有效范围内
        effectiveAngle = std::max(0.0, std::min(effectiveAngle, maxEffectiveAngle));

        // 使用正弦函数模拟重力影响 - 更符合物理规律
        double angleRad = effectiveAngle * M_PI / 180.0;
        double gravityFactor = std::sin(angleRad);

        // 基础流速随角度增加
        double baseFlowRate = maxFlowRate * gravityFactor;

        // 添加非线性增强，大角度时流速显著增加
        double normalizedAngle = effectiveAngle / maxEffectiveAngle;
        double nonLinearFactor = 1.0 + normalizedAngle * normalizedAngle * 0.5;

        double flowRate = baseFlowRate * nonLinearFactor;

        // 根据剩余液体量限制流速
        double volumeLimit = currentVolume * 10.0;
        flowRate = std::min(flowRate, volumeLimit);

        // 确保最小流速（避免过小的流速）
        if (flowRate > 0.0) {
            flowRate = std::max(flowRate, maxFlowRate * 0.01); // 至少1%的最大流速
        }

        return flowRate;
    }

    double simulatePouring(double pouringAngle, double deltaTime) {
        double flowRate = calculateFlowRate(pouringAngle);
        double volumePoured = flowRate * deltaTime;

        // Limit to available volume
        volumePoured = std::min(volumePoured, currentVolume);
        currentVolume -= volumePoured;

        // Convert volume to weight (grams)
        return volumePoured * liquidDensity;
    }

    double getRemainingVolume() const { return currentVolume; }
    double getTotalVolume() const { return containerVolume; }

    void reset() { currentVolume = containerVolume; }
};

class PouringRobotController {
private:
    WorkCell::Ptr workcell;
    Device::Ptr robot;
    State state;
    Frame* tcpFrame;

    // 烧杯位置参数
    Vector3D<> beakerCenter;
    double beakerRadius;
    double pouringHeight;

    // HTTP客户端参数
    std::string serverHost;
    int serverPort;
    httplib::Client httpClient;

    // PID控制和模拟组件
    PIDController pidController;
    SimulatedScale scale;
    LiquidFlowSimulator liquidSimulator;

    // 控制参数
    double targetWeight;
    double currentPouringAngle;
    double maxPouringAngle;
    double minPouringAngle;
    bool pouringActive;
    
public:
    PouringRobotController(const std::string& workcellFile, const std::string& host = "localhost", int port = 8080)
        : serverHost(host), serverPort(port), httpClient(host, port),
          pidController(0.8, 0.1, 0.05), scale(0.02), liquidSimulator(500.0, 1.0, 15.0, 5.0),
          targetWeight(0.0), currentPouringAngle(0.0), maxPouringAngle(45.0),
          minPouringAngle(0.0), pouringActive(false) {
        // 加载WorkCell
        workcell = WorkCellLoader::Factory::load(workcellFile);
        if (workcell == nullptr) {
            throw std::runtime_error("Failed to load WorkCell: " + workcellFile);
        }

        // 获取机器人设备
        robot = workcell->findDevice("FourAxisRobot");
        if (robot == nullptr) {
            throw std::runtime_error("Robot 'FourAxisRobot' not found in WorkCell");
        }

        // 获取TCP框架
        tcpFrame = workcell->findFrame("FourAxisRobot.TCP");
        if (tcpFrame == nullptr) {
            throw std::runtime_error("TCP frame not found");
        }

        // 初始化状态
        state = workcell->getDefaultState();

        // 设置烧杯参数 (根据fourRobot.xml中的m_box位置调整)
        beakerCenter = Vector3D<>(0, 0, 0);  // 烧杯中心位置 (来自fourRobot.xml)
        beakerRadius = 0.025;  // 烧杯半径 2.5cm
        pouringHeight = 0.05; // 倒样品时的高度

        // 设置HTTP客户端超时
        httpClient.set_connection_timeout(5, 0); // 5秒连接超时
        httpClient.set_read_timeout(10, 0);      // 10秒读取超时

        std::cout << "Four-Axis Pouring Robot Controller initialized successfully!" << std::endl;
        std::cout << "Robot DOF: " << robot->getDOF() << std::endl;
        std::cout << "Robot Configuration:" << std::endl;
        std::cout << "  J1: 左右移动 X方向 (Left-right X movement, 0 to 0.3m)" << std::endl;
        std::cout << "  J2: 上下移动 Z方向 (Vertical Z movement, -0.2 to 0m)" << std::endl;
        std::cout << "  J3: 倾倒旋转 (Pouring rotation, 0°完全倾倒, -180°瓶口抬起)" << std::endl;
        std::cout << "  J4: 料瓶旋转 (Bottle rotation, 控制转速)" << std::endl;
        std::cout << "Beaker position: (" << beakerCenter[0] << ", " << beakerCenter[1] << ", " << beakerCenter[2] << ")" << std::endl;
        std::cout << "HTTP Server: " << serverHost << ":" << serverPort << std::endl;
    }
    
    // 直接设置关节角度 - 四轴机器人（修正坐标系）
    Q calculateJointAngles(double xPosition = 0.0, double zPosition = 0.0,
                          double pouringAngle = 0.0, double bottleRotation = 0.0) {
        Q q(robot->getDOF());

        // J1 (左右移动): X方向位置 (米)
        q[0] = xPosition;

        // J2 (上下移动): Z方向位置 (米)
        q[1] = zPosition;

        // J3 (倾倒旋转): 倾倒角度转关节角度
        // 物理倾倒角度增大 = 关节角度值减小 (顺时针旋转)
        // 关节角度 = -倾倒角度 (度转弧度)
        q[2] = -pouringAngle * M_PI / 180.0;

        // J4 (旋转料瓶): 瓶子旋转角度 (度转弧度)
        q[3] = bottleRotation * M_PI / 180.0;

        // 检查关节限制
        Q qMin = robot->getBounds().first;
        Q qMax = robot->getBounds().second;

        for (size_t i = 0; i < q.size(); i++) {
            if (q[i] < qMin[i]) {
                q[i] = qMin[i];
                std::cout << "Warning: Joint " << i << " limited to minimum value: " << qMin[i] << std::endl;
            }
            if (q[i] > qMax[i]) {
                q[i] = qMax[i];
                std::cout << "Warning: Joint " << i << " limited to maximum value: " << qMax[i] << std::endl;
            }
        }

        return q;
    }

    // 兼容性函数 - 保持向后兼容 (两参数版本)
    Q calculateJointAnglesTwoAxis(double linearPosition, double pouringAngle) {
        // 对于四轴机器人，将原来的线性位置映射到Y位置（前后移动）
        return calculateJointAngles(0.0, linearPosition, pouringAngle, 0.0);
    }

    // 倾倒序列的关节角度计算 - TCP位置保持版本 (TCP始终保持在烧杯中心固定位置)
    Q calculatePouringJointAngles(double pouringAngle = 0.0, double bottleRotation = 0.0) {
        // TCP到旋转中心的距离 (需要根据实际机器人结构调整)
        double tcpOffset = 0.14; // TCP相对于旋转轴的偏移距离 (根据fourRobot.xml中的TCP位置)

        // 目标TCP位置 - 始终保持在烧杯中心上方固定位置
        Vector3D<> targetTcpPosition = beakerCenter;
        targetTcpPosition[2] += pouringHeight; // 在烧杯中心上方固定高度

        // 计算旋转时TCP的偏移量
        // 注意：pouringAngle是物理倾倒角度，关节角度 = -pouringAngle
        double jointAngleRad = -pouringAngle * M_PI / 180.0; // 关节角度（弧度）

        // 计算倾倒时TCP相对于旋转轴的位置变化
        // 根据新的坐标系：J1控制X方向（左右），J2控制Z方向（上下）
        double yTcpOffset = tcpOffset * sin(jointAngleRad);  // TCP在Y方向的偏移（前后）
        double zTcpOffset = tcpOffset * (1.0 - cos(jointAngleRad)); // TCP在Z方向的偏移（上下）

        // 计算为了保持TCP在目标位置所需的机器人基座调整
        // 关键：确保TCP的X,Y,Z坐标始终保持在烧杯中心，不受倾倒角度影响

        // X方向（左右）：保持TCP的X坐标在烧杯中心
        double xPosition = targetTcpPosition[0]; // J1: 左右位置

        // Z方向（上下）：补偿TCP的Z偏移，使TCP始终保持在固定高度
        double zPosition = targetTcpPosition[2] - zTcpOffset; // J2: 上下调整

        // X方向保持不变，因为倾倒旋转不影响X坐标（假设旋转轴平行于X轴）

        std::cout << "=== TCP位置保持计算 ===" << std::endl;
        std::cout << "目标倾倒角度: " << pouringAngle << "deg (关节角度: " << (-pouringAngle) << "deg)" << std::endl;
        std::cout << "烧杯中心位置: (" << beakerCenter[0] << ", " << beakerCenter[1] << ", " << beakerCenter[2] << ")" << std::endl;
        std::cout << "目标TCP位置: (" << targetTcpPosition[0] << ", " << targetTcpPosition[1] << ", " << targetTcpPosition[2] << ")" << std::endl;
        std::cout << "TCP偏移 - Y方向: " << yTcpOffset << "m, Z方向: " << zTcpOffset << "m" << std::endl;
        std::cout << "机器人调整 - Z位置(J1): " << zPosition << "m, Y位置(J2): " << yPosition << "m" << std::endl;
        std::cout << "瓶子旋转角度(J4): " << bottleRotation << "deg" << std::endl;

        // 验证计算：显示期望的TCP位置应该是固定的
        std::cout << "期望TCP位置应该始终为: (" << targetTcpPosition[0] << ", " << targetTcpPosition[1] << ", " << targetTcpPosition[2] << ")" << std::endl;

        return calculateJointAngles(xPosition, zPosition, pouringAngle, bottleRotation);
    }

    // 平滑移动功能 - 将大的角度变化分解为多个小步骤
    void executeSmoothedMovement(double startAngle, double endAngle, int steps = 5) {
        // if (steps <= 1) {
            Q q = calculatePouringJointAngles(endAngle);
            moveToPosition(q);
            return;
        // }

        // double angleStep = (endAngle - startAngle) / steps;
        //
        // for (int i = 1; i <= steps; i++) {
        //     double currentAngle = startAngle + angleStep * i;
        //     Q q = calculatePouringJointAngles(currentAngle);
        //     moveToPosition(q);
        //
        //     // 每步之间短暂停顿，让机器人稳定
        //     std::this_thread::sleep_for(std::chrono::milliseconds(10));
        // }
    }
    


    // PID控制的精确倾倒功能 - TCP位置保持版本
    void executePIDControlledPouring(double targetWeightGrams, double maxTimeSeconds = 45.0) {
        std::cout << "\n=== Starting PID Controlled Pouring with TCP Position Maintenance ===" << std::endl;
        std::cout << "功能特点：TCP始终保持在烧杯中心固定位置，只改变倾倒姿态" << std::endl;
        std::cout << "Target weight: " << targetWeightGrams << " grams" << std::endl;
        std::cout << "Maximum time: " << maxTimeSeconds << " seconds" << std::endl;
        std::cout << "TCP目标位置: (" << beakerCenter[0] << ", " << beakerCenter[1] << ", " << (beakerCenter[2] + pouringHeight) << ")" << std::endl;

        // 重置系统状态
        targetWeight = targetWeightGrams;
        pidController.reset();
        scale.reset();
        liquidSimulator.reset();
        currentPouringAngle = 0.0;
        pouringActive = true;

        // 移动到烧杯上方初始位置
        std::cout << "Moving to initial position above beaker..." << std::endl;
        Q initialQ = calculatePouringJointAngles(0.0);
        moveToPosition(initialQ);
        std::this_thread::sleep_for(std::chrono::milliseconds(10));

        // 根据目标重量动态调整初始角度
        double initialAngleRatio = 0.3; // 默认30%最大角度

        if (targetWeight <= 5.0) {
            initialAngleRatio = 0.15; // 5g以下：15%最大角度
        } else if (targetWeight <= 10.0) {
            initialAngleRatio = 0.25; // 10g以下：25%最大角度
        } else if (targetWeight <= 20.0) {
            initialAngleRatio = 0.35; // 20g以下：35%最大角度
        } else if (targetWeight <= 50.0) {
            initialAngleRatio = 0.45; // 50g以下：45%最大角度
        } else {
            initialAngleRatio = 0.6; // 大重量：60%最大角度
        }

        double initialPouringAngle = maxPouringAngle * initialAngleRatio;
        std::cout << "Starting with adaptive angle: " << initialPouringAngle << " degrees ("
                  << (initialAngleRatio * 100) << "% of max) for target " << targetWeight << "g" << std::endl;

        currentPouringAngle = initialPouringAngle;
        Q startPouringQ = calculatePouringJointAngles(currentPouringAngle);
        moveToPosition(startPouringQ);
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        auto startTime = std::chrono::steady_clock::now();
        auto lastUpdateTime = startTime;
        double currentWeight = 0.0;
        double previousAngle = 0.0;
        int stepCount = 0;

        std::cout << "\nStarting DYNAMIC PID control loop..." << std::endl;
        std::cout << "Strategy: Dynamic angle calculation based on error, flow rate, prediction, and time" << std::endl;
        std::cout << "Note: Pouring angle increase = Joint2 clockwise (negative values)" << std::endl;
        std::cout << "Step\tTime(s)\tWeight(g)\tError%\tTargetAngle\tAngle(deg)\tΔAngle\tFlow(ml/s)\tPredicted(g)" << std::endl;
        std::cout << "------------------------------------------------------------------------------------------------" << std::endl;

        while (pouringActive) {
            auto currentTime = std::chrono::steady_clock::now();
            double elapsedTime = std::chrono::duration<double>(currentTime - startTime).count();
            double deltaTime = std::chrono::duration<double>(currentTime - lastUpdateTime).count();
            stepCount++;

            // 检查超时
            if (elapsedTime > maxTimeSeconds) {
                std::cout << "Timeout reached, stopping pouring..." << std::endl;
                break;
            }

            // 模拟液体流动和重量增加
            if (currentPouringAngle > minPouringAngle) {
                double weightAdded = liquidSimulator.simulatePouring(currentPouringAngle, deltaTime);
                scale.addWeight(weightAdded);
            }

            // 获取当前重量（带噪声）
            currentWeight = scale.getWeight();

            // 检查是否达到目标重量 - 预测性停止
            double currentFlowRate = liquidSimulator.calculateFlowRate(currentPouringAngle);
            double predictedWeight = currentWeight + (currentFlowRate * 0.4); // 预测0.4秒后重量

            if (currentWeight >= targetWeight) {
                std::cout << "Target weight reached!" << std::endl;
                break;
            } else if (predictedWeight >= targetWeight && currentPouringAngle > 0) {
                std::cout << "Predicted overshoot, stopping early!" << std::endl;
                break;
            }

            // 检查是否还有液体
            if (liquidSimulator.getRemainingVolume() <= 0.0) {
                std::cout << "Container is empty!" << std::endl;
                break;
            }

            // 基于重量误差的角度控制策略
            double weightError = targetWeight - currentWeight;
            double errorRatio = weightError / targetWeight;

            previousAngle = currentPouringAngle;

            // 倾倒策略：根据重量误差逐渐减小角度
            double targetAngle = 0.0;

            // 预测性控制 - 考虑当前流速和剩余时间
            currentFlowRate = liquidSimulator.calculateFlowRate(currentPouringAngle);

            // 动态调整预测时间：小重量目标和接近目标时预测时间更长
            double predictTime = 0.3; // 基础预测时间

            // 根据目标重量调整预测时间
            if (targetWeight <= 10.0) {
                predictTime = 0.6; // 小重量目标预测更长时间
            } else if (targetWeight <= 25.0) {
                predictTime = 0.4; // 中等重量目标
            }

            // 根据误差比例进一步调整
            if (errorRatio < 0.3) predictTime *= 1.5; // 接近目标时预测更长时间
            else if (errorRatio < 0.1) predictTime *= 2.0; // 非常接近时预测更长时间

            predictedWeight = currentWeight + (currentFlowRate * predictTime);
            double predictedError = (targetWeight - predictedWeight) / targetWeight;

            if (predictedWeight >= targetWeight) {
                // 预测会超重，立即停止
                targetAngle = 0.0;
            } else {
                // 动态角度计算 - 基于多个因素的连续函数

                // 1. 基于重量误差的基础角度（非线性函数）
                double baseAngle = maxPouringAngle * std::pow(errorRatio, 1.5); // 非线性衰减

                // 2. 根据目标重量调整最大角度限制
                double maxAllowedAngle = maxPouringAngle;
                if (targetWeight <= 5.0) {
                    maxAllowedAngle = maxPouringAngle * 0.4;
                } else if (targetWeight <= 15.0) {
                    maxAllowedAngle = maxPouringAngle * (0.4 + 0.3 * (targetWeight - 5.0) / 10.0);
                } else if (targetWeight <= 50.0) {
                    maxAllowedAngle = maxPouringAngle * (0.7 + 0.3 * (targetWeight - 15.0) / 35.0);
                }

                // 3. 根据当前流速调整（流速越大，角度应该越小）
                double flowRateRatio = currentFlowRate / 15.0; // 归一化流速
                double flowAdjustment = 1.0 - (flowRateRatio * 0.3); // 流速大时减小角度

                // 4. 根据预测误差调整（预测越接近目标，角度越小）
                double predictedError = std::abs(predictedWeight - targetWeight) / targetWeight;
                double predictAdjustment = 1.0 - (predictedError * 0.5);

                // 5. 时间因子（倾倒时间越长，越保守）
                double timeAdjustment = 1.0;
                if (elapsedTime > 10.0) {
                    timeAdjustment = std::max(0.5, 1.0 - (elapsedTime - 10.0) * 0.05);
                }

                // 综合计算目标角度
                targetAngle = baseAngle * flowAdjustment * predictAdjustment * timeAdjustment;
                targetAngle = std::min(targetAngle, maxAllowedAngle);
                targetAngle = std::max(targetAngle, 0.0);

                // 最小角度阈值 - 太小的角度直接设为0
                if (targetAngle < maxPouringAngle * 0.02) {
                    targetAngle = 0.0;
                }
            }

            // 动态角度调整 - 使用PID控制器平滑调整
            previousAngle = currentPouringAngle;

            // 使用PID控制器计算角度调整
            double pidOutput = pidController.calculate(targetAngle, currentPouringAngle);

            // 动态调整PID输出的增益
            double dynamicGain = 0.3; // 基础增益

            // 根据误差大小调整增益
            if (errorRatio > 0.5) {
                dynamicGain = 0.5; // 误差大时响应快
            } else if (errorRatio < 0.2) {
                dynamicGain = 0.15; // 误差小时响应慢，更稳定
            }

            // 根据角度差异调整增益
            double angleDiff = std::abs(targetAngle - currentPouringAngle);
            if (angleDiff > 5.0) {
                dynamicGain *= 1.5; // 角度差异大时加快调整
            } else if (angleDiff < 1.0) {
                dynamicGain *= 0.7; // 角度差异小时减慢调整
            }

            double angleAdjustment = pidOutput * dynamicGain;

            // 动态限制单次调整幅度
            double maxAdjustment = 1.5; // 基础最大调整
            if (errorRatio < 0.1) {
                maxAdjustment = 0.3; // 接近目标时限制调整幅度
            } else if (errorRatio < 0.3) {
                maxAdjustment = 0.8; // 中等误差时
            }

            angleAdjustment = std::max(-maxAdjustment, std::min(maxAdjustment, angleAdjustment));

            currentPouringAngle += angleAdjustment;

            // 限制倾倒角度范围
            currentPouringAngle = std::max(minPouringAngle, std::min(maxPouringAngle, currentPouringAngle));

            // 计算当前流速用于显示
            currentFlowRate = liquidSimulator.calculateFlowRate(currentPouringAngle);

            // 执行机器人动作 - 分步移动以增加精度
            executeSmoothedMovement(previousAngle, currentPouringAngle, 3);

            // 输出状态信息
            double angleDelta = currentPouringAngle - previousAngle;
            std::cout << std::fixed << std::setprecision(2)
                      << stepCount << "\t"
                      << elapsedTime << "\t"
                      << currentWeight << "\t"
                      << std::setprecision(1) << (errorRatio * 100) << "%\t"
                      << std::setprecision(1) << targetAngle << "\t\t"
                      << std::setprecision(2) << currentPouringAngle << "\t\t"
                      << std::setprecision(3) << angleDelta << "\t"
                      << std::setprecision(2) << currentFlowRate << "\t"
                      << predictedWeight << std::endl;

            lastUpdateTime = currentTime;
            std::this_thread::sleep_for(std::chrono::milliseconds(250)); // 降低到4Hz，更稳定精确
        }

        // 倾倒完成，回到垂直位置
        std::cout << "\nPouring completed, returning to vertical position..." << std::endl;
        currentPouringAngle = 0.0;
        Q finalQ = calculatePouringJointAngles(0.0);
        moveToPosition(finalQ);
        pouringActive = false;

        // 输出最终结果
        std::cout << "\n=== Pouring Results ===" << std::endl;
        std::cout << "Final weight: " << currentWeight << " grams" << std::endl;
        std::cout << "Target weight: " << targetWeight << " grams" << std::endl;
        std::cout << "Weight error: " << (currentWeight - targetWeight) << " grams" << std::endl;
        std::cout << "Remaining liquid: " << liquidSimulator.getRemainingVolume() << " ml" << std::endl;
        std::cout << "=======================" << std::endl;
    }



    // 设置PID参数
    void setPIDParameters(double kp, double ki, double kd) {
        pidController.setParameters(kp, ki, kd);
        std::cout << "PID parameters updated: Kp=" << kp << ", Ki=" << ki << ", Kd=" << kd << std::endl;
    }

    // 设置倾倒角度限制
    void setPouringAngleLimits(double minAngle, double maxAngle) {
        minPouringAngle = minAngle;
        maxPouringAngle = maxAngle;
        std::cout << "Pouring angle limits: [" << minAngle << ", " << maxAngle << "] degrees" << std::endl;
    }

    // 获取当前模拟重量
    double getCurrentWeight() {
        return scale.getWeight();
    }

    // 重置模拟系统
    void resetSimulation() {
        pidController.reset();
        scale.reset();
        liquidSimulator.reset();
        currentPouringAngle = 0.0;
        pouringActive = false;
        std::cout << "Simulation system reset." << std::endl;
    }

    // 测试流速计算函数
    void testFlowRateCalculation() {
        std::cout << "\n=== Flow Rate Calculation Test ===" << std::endl;
        std::cout << "Testing flow rate vs pouring angle relationship:" << std::endl;
        std::cout << "Note: Pouring angle increase = Joint2 clockwise rotation (negative values)" << std::endl;
        std::cout << "Pouring Angle(deg)\tJoint2 Angle(deg)\tFlow Rate(ml/s)\tNormalized Flow" << std::endl;
        std::cout << "------------------------------------------------------------------------" << std::endl;

        // 重置液体模拟器以确保满容量
        liquidSimulator.reset();

        for (double pouringAngle = 0.0; pouringAngle <= 50.0; pouringAngle += 2.5) {
            double flowRate = liquidSimulator.calculateFlowRate(pouringAngle);
            double normalizedFlow = flowRate / 15.0; // 假设最大流速为15ml/s
            double joint2Angle = -pouringAngle; // 关节角度 = -倾倒角度

            std::cout << std::fixed << std::setprecision(1) << pouringAngle << "\t\t\t"
                      << joint2Angle << "\t\t\t"
                      << std::setprecision(2) << flowRate << "\t\t"
                      << std::setprecision(3) << normalizedFlow << std::endl;
        }

        std::cout << "\nAs pouring angle increases (joint2 becomes more negative):" << std::endl;
        std::cout << "- Flow rate increases (physics-based)" << std::endl;
        std::cout << "- Joint2 clockwise rotation = negative angle values" << std::endl;
        std::cout << "=====================================================" << std::endl;
    }
    
    // 移动到指定关节位置并发送到服务器
    void moveToPosition(const Q& q) {
        robot->setQ(q, state);

        std::cout << "Joint values: [";
        for (size_t i = 0; i < q.size(); i++) {
            if (i == 0) {
                std::cout << "J1(左右X): " << q[i] << "m";
            } else if (i == 1) {
                std::cout << ", J2(上下Z): " << q[i] << "m";
            } else if (i == 2) {
                double jointAngleDeg = q[i] * 180 / M_PI;
                double pouringAngleDeg = -jointAngleDeg; // 物理倾倒角度 = -关节角度
                std::cout << ", J3(倾倒): " << jointAngleDeg << "deg (倾倒角度: " << pouringAngleDeg << "deg)";
            } else if (i == 3) {
                double bottleRotationDeg = q[i] * 180 / M_PI;
                std::cout << ", J4(料瓶旋转): " << bottleRotationDeg << "deg";
            } else {
                std::cout << ", Joint" << i << ": " << q[i];
            }
        }
        std::cout << "]" << std::endl;

        // 发送关节角度到服务器
        sendJointAnglesToServer(q);
    }

    // 发送关节角度到HTTP服务器
    void sendJointAnglesToServer(const Q& q) {
        try {
            // 构建关节角度字符串
            std::stringstream ss;
            for (size_t i = 0; i < q.size(); i++) {
                if (i > 0) ss << ",";
                ss << q[i];
            }
            std::string jointAnglesStr = ss.str();

            std::cout << "Sending to server: " << jointAnglesStr << std::endl;

            // 发送POST请求
            auto res = httpClient.Post("/JointAngles", jointAnglesStr, "text/plain");

            if (res) {
                if (res->status == 200) {
                    std::cout << "Server response: " << res->body << std::endl;
                } else {
                    std::cout << "Server error: " << res->status << " - " << res->body << std::endl;
                }
            } else {
                std::cout << "Failed to connect to server at " << serverHost << ":" << serverPort << std::endl;
            }

        } catch (const std::exception& e) {
            std::cout << "HTTP request error: " << e.what() << std::endl;
        }
        Sleep(200);
    }
    
    // 打印当前TCP位姿
    void printCurrentPose() {
        Transform3D<> tcpTransform = tcpFrame->getTransform(state);
        Vector3D<> pos = tcpTransform.P();
        RPY<> rpy(tcpTransform.R());

        std::cout << "TCP位置: (" << pos[0] << ", " << pos[1] << ", " << pos[2] << ")" << std::endl;
        std::cout << "TCP姿态: (" << rpy[0]*180/M_PI << "°, " << rpy[1]*180/M_PI << "°, " << rpy[2]*180/M_PI << "°)" << std::endl;

        // 计算与烧杯中心的距离
        Vector3D<> diff = pos - beakerCenter;
        diff[2] = 0; // 只考虑水平距离
        double distance = diff.norm2();
        std::cout << "与烧杯中心水平距离: " << distance << " m" << std::endl;

        // 验证TCP位置是否保持在目标位置
        Vector3D<> targetTcpPosition = beakerCenter;
        targetTcpPosition[2] += pouringHeight;

        double xError = pos[0] - targetTcpPosition[0];
        double yError = pos[1] - targetTcpPosition[1];
        double zError = pos[2] - targetTcpPosition[2];

        std::cout << "=== TCP位置验证 ===" << std::endl;
        std::cout << "目标TCP位置: (" << targetTcpPosition[0] << ", " << targetTcpPosition[1] << ", " << targetTcpPosition[2] << ")" << std::endl;
        std::cout << "实际TCP位置: (" << pos[0] << ", " << pos[1] << ", " << pos[2] << ")" << std::endl;
        std::cout << "位置误差 - X: " << xError << "m, Y: " << yError << "m, Z: " << zError << "m" << std::endl;
        std::cout << "总位置误差: " << sqrt(xError*xError + yError*yError + zError*zError) << "m" << std::endl;
    }

    // 验证TCP位置保持功能
    void verifyTcpPositionMaintenance() {
        std::cout << "\n=== TCP位置保持验证测试 ===" << std::endl;
        std::cout << "测试不同倾倒角度下TCP是否始终保持在烧杯中心固定高度" << std::endl;
        std::cout << "烧杯中心位置: (" << beakerCenter[0] << ", " << beakerCenter[1] << ", " << beakerCenter[2] << ")" << std::endl;
        std::cout << "目标倾倒高度: " << pouringHeight << " m" << std::endl;
        std::cout << "目标TCP高度: " << (beakerCenter[2] + pouringHeight) << " m" << std::endl;
        std::cout << "\n角度(deg)\tTCP_X(m)\tTCP_Y(m)\tTCP_Z(m)\t水平误差(m)\t高度误差(m)" << std::endl;
        std::cout << "---------------------------------------------------------------------------------" << std::endl;

        // 测试不同倾倒角度
        std::vector<double> testAngles = {0.0, 10.0, 20.0, 30.0, 40.0, 45.0};
        double targetHeight = beakerCenter[2] + pouringHeight;

        for (double angle : testAngles) {
            // 计算并移动到该角度
            Q q = calculatePouringJointAngles(angle);
            moveToPosition(q);

            // 获取实际TCP位置
            Transform3D<> tcpTransform = tcpFrame->getTransform(state);
            Vector3D<> pos = tcpTransform.P();

            // 计算误差
            Vector3D<> horizontalDiff = pos - beakerCenter;
            horizontalDiff[2] = 0; // 只考虑水平误差
            double horizontalError = horizontalDiff.norm2();
            double heightError = pos[2] - targetHeight;

            // 输出结果
            std::cout << std::fixed << std::setprecision(1) << angle << "\t\t"
                      << std::setprecision(4) << pos[0] << "\t\t" << pos[1] << "\t\t" << pos[2] << "\t\t"
                      << horizontalError << "\t\t" << heightError << std::endl;

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }

        std::cout << "\n验证完成！理想情况下：" << std::endl;
        std::cout << "- 水平误差应该接近0（TCP始终对准烧杯中心）" << std::endl;
        std::cout << "- 高度误差应该接近0（TCP始终保持在固定高度）" << std::endl;
        std::cout << "=====================================================" << std::endl;
    }
    
    // 设置烧杯位置
    void setBeakerPosition(const Vector3D<>& center, double radius) {
        beakerCenter = center;
        beakerRadius = radius;
        std::cout << "烧杯位置设置为: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
        std::cout << "烧杯半径: " << radius << " m" << std::endl;
    }

    // 设置倾倒高度
    void setPouringHeight(double height) {
        pouringHeight = height;
        std::cout << "倾倒高度设置为: " << height << " m" << std::endl;
        std::cout << "目标TCP高度: " << (beakerCenter[2] + pouringHeight) << " m" << std::endl;
    }
    
    // 手动控制模式
    void manualControl() {
        std::cout << "\n=== Manual Control Mode ===" << std::endl;
        std::cout << "Available commands:" << std::endl;
        std::cout << "  move <linear_pos> <rotation_angle> - Move to specified linear position (m) and rotation angle (deg)" << std::endl;
        std::cout << "  pour <angle> - Execute pouring action to specified angle (deg)" << std::endl;
        std::cout << "  pidpour <target_weight> - Execute PID controlled pouring with TCP position maintenance (grams)" << std::endl;
        std::cout << "  setpid <kp> <ki> <kd> - Set PID parameters" << std::endl;
        std::cout << "  setlimits <min_angle> <max_angle> - Set pouring angle limits (degrees)" << std::endl;
        std::cout << "  setheight <height> - Set pouring height above beaker (meters)" << std::endl;
        std::cout << "  weight - Show current simulated weight" << std::endl;
        std::cout << "  reset - Reset simulation system" << std::endl;
        std::cout << "  testflow - Test flow rate calculation" << std::endl;
        std::cout << "  verifytcp - Verify TCP position maintenance during pouring" << std::endl;
        std::cout << "  home - Return to initial position" << std::endl;
        std::cout << "  test - Test server connection" << std::endl;
        std::cout << "  quit - Exit" << std::endl;
        std::cout << "\nJoint limits info:" << std::endl;
        Q qMin = robot->getBounds().first;
        Q qMax = robot->getBounds().second;
        std::cout << "  Joint 1 (linear): [" << qMin[0] << ", " << qMax[0] << "] m" << std::endl;
        std::cout << "  Joint 2 (rotation): [" << qMin[1]*180/M_PI << ", " << qMax[1]*180/M_PI << "] deg" << std::endl;
        std::cout << "  Note: Joint2 clockwise rotation (negative values) = Pouring angle increase" << std::endl;
        std::cout << "        Physical pouring angle = -Joint2 angle" << std::endl;

        std::string command;
        while (std::cin >> command && command != "quit") {
            if (command == "move") {
                double linear_pos, rotation_angle;
                std::cin >> linear_pos >> rotation_angle;
                Q q = calculateJointAnglesTwoAxis(linear_pos, rotation_angle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "pour") {
                double angle;
                std::cin >> angle;
                Q q = calculatePouringJointAngles(angle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "pidpour") {
                double target_weight;
                std::cin >> target_weight;
                std::cout << "执行TCP位置保持的PID控制倾倒..." << std::endl;
                executePIDControlledPouring(target_weight);
            }
            else if (command == "setpid") {
                double kp, ki, kd;
                std::cin >> kp >> ki >> kd;
                setPIDParameters(kp, ki, kd);
            }
            else if (command == "setlimits") {
                double min_angle, max_angle;
                std::cin >> min_angle >> max_angle;
                setPouringAngleLimits(min_angle, max_angle);
            }
            else if (command == "setheight") {
                double height;
                std::cin >> height;
                setPouringHeight(height);
            }
            else if (command == "weight") {
                std::cout << "Current simulated weight: " << getCurrentWeight() << " grams" << std::endl;
            }
            else if (command == "reset") {
                resetSimulation();
            }
            else if (command == "testflow") {
                testFlowRateCalculation();
            }
            else if (command == "verifytcp") {
                verifyTcpPositionMaintenance();
            }
            else if (command == "home") {
                Q q = calculateJointAngles(0.0, 0.0, 0.0, 0.0); // 四轴机器人回到原点
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "move4") {
                double zPos, yPos, pouring, bottle;
                std::cout << "输入四轴位置 (Z上下 Y前后 倾倒角度 瓶子旋转角度): ";
                std::cin >> zPos >> yPos >> pouring >> bottle;
                Q q = calculateJointAngles(zPos, yPos, pouring, bottle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "tilt") {
                double angle;
                std::cout << "输入倾倒角度 (-90到60度): ";
                std::cin >> angle;
                Q q = calculateJointAngles(0.0, 0.0, angle, 0.0);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "rotate") {
                double angle;
                std::cout << "输入瓶子旋转角度 (-360到360度): ";
                std::cin >> angle;
                Q q = calculateJointAngles(0.0, 0.0, 0.0, angle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "test") {
                testServerConnection();
            }
            else {
                std::cout << "Unknown command: " << command << std::endl;
                std::cout << "Available commands:" << std::endl;
                std::cout << "  move <linear_pos> <rotation_angle> - 移动机器人(兼容模式)" << std::endl;
                std::cout << "  move4 - 四轴移动 (上下 前后 倾倒 旋转)" << std::endl;
                std::cout << "  tilt - 倾倒动作 (-90到60度)" << std::endl;
                std::cout << "  rotate - 瓶子旋转 (-360到360度)" << std::endl;
                std::cout << "  pour <angle> - 倾倒操作" << std::endl;
                std::cout << "  verifytcp - 验证TCP位置保持功能" << std::endl;
                std::cout << "  home - 回到原点" << std::endl;
                std::cout << "  test - 测试服务器连接" << std::endl;
                std::cout << "  quit - 退出程序" << std::endl;
            }
            std::cout << "\nEnter next command: ";
        }
    }

    // 测试服务器连接
    void testServerConnection() {
        std::cout << "Testing server connection..." << std::endl;

        // 测试获取设备名称
        auto res = httpClient.Get("/DeviceNames");
        if (res) {
            if (res->status == 200) {
                std::cout << "Server connection OK!" << std::endl;
                std::cout << "Available devices: " << res->body << std::endl;
            } else {
                std::cout << "Server error: " << res->status << std::endl;
            }
        } else {
            std::cout << "Failed to connect to server!" << std::endl;
        }
    }
};

int main(int argc, char** argv) {
    try {
        // 解析命令行参数
        std::string workcellFile = "C:/Users/<USER>/Desktop/updateload/fourRobot.xml";
        std::string serverHost = "127.0.0.1";
        int serverPort = 8080;

        // if (argc > 1) {
        //     workcellFile = argv[1];
        // }
        // if (argc > 2) {
        //     serverHost = argv[2];
        // }
        // if (argc > 3) {
        //     serverPort = std::stoi(argv[3]);
        // }

        std::cout << "Loading WorkCell file: " << workcellFile << std::endl;
        std::cout << "Target server: " << serverHost << ":" << serverPort << std::endl;

        // 创建控制器
        PouringRobotController controller(workcellFile, serverHost, serverPort);

        // 设置烧杯位置 (根据新的关节限制调整)
        // J1范围：0到0.3m (左右移动)，J2范围：-0.2到0m (上下移动)
        // 新烧杯位置：(0.140, 0.370, 0.328)
        controller.setBeakerPosition(Vector3D<>(0.140, 0.370, 0.328), 0.025);  // 使用新的烧杯位置
        controller.setPouringHeight(0.03);  // 倾倒高度3cm

        // 测试服务器连接
        controller.testServerConnection();

        // 设置角度控制的PID参数和倾倒限制
        controller.setPIDParameters(0.8, 0.1, 0.05); // 适合角度控制的PID参数
        controller.setPouringAngleLimits(0.0, 40.0);   // 降低最大角度以提高精度

        // 直接启动基础PID控制倾倒演示 - TCP位置保持版本
        std::cout << "\nStarting PID controlled pouring with TCP position maintenance..." << std::endl;
        std::cout << "功能：在倾倒过程中TCP始终保持在烧杯中心固定位置，只改变倾倒姿态" << std::endl;
        std::cout << "Demo 1: Pour 8 grams" << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));
        controller.executePIDControlledPouring(8.0);

        std::cout << "\nWait 3 seconds before next demo..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(3));

        std::cout << "Demo 2: Pour 15 grams" << std::endl;
        controller.resetSimulation();
        controller.executePIDControlledPouring(15.0);

        // 进入手动控制模式
        controller.manualControl();

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
