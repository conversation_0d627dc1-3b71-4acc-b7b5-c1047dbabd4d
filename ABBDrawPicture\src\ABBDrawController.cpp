#include "ABBDrawController.h"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>

namespace ABBDraw {

ABBDrawController::ABBDrawController()
    : drawSpeed_(5.0), drawZone_(1), drawAcc_(100), liftHeight_(0.001) {
    imageProcessor_ = std::make_unique<ImageProcessor>();
    trajectoryOptimizer_ = std::make_unique<TrajectoryOptimizer>();
    robotDriver_ = std::make_unique<AbbRobotDriverEgmUpdate>();
}

ABBDrawController::~ABBDrawController() {
    if (robotDriver_ && robotDriver_->isConnect()) {
        robotDriver_->disconnect();
    }
}

bool ABBDrawController::initialize(const std::string& robotIP, int robotPort) {
    std::cout << "初始化ABB绘图控制器..." << std::endl;
    std::cout << "机器人IP: " << robotIP << ", 端口: " << robotPort << std::endl;

    // 连接机器人
    bool connected = robotDriver_->connect(robotIP, robotPort);
    if (!connected) {
        std::cerr << "错误：无法连接到机器人" << std::endl;
        return false;
    }

    std::cout << "机器人连接成功" << std::endl;
    return true;
}

bool ABBDrawController::executeDrawing(const std::string& imagePath,
                                      double scaleFactor,
                                      int targetSize,
                                      double drawHeight) {
    std::cout << "开始执行绘图任务..." << std::endl;

    // 1. 图像处理
    auto points = imageProcessor_->processImage(imagePath, scaleFactor, targetSize);
    if (points.empty()) {
        std::cerr << "错误：图像处理失败，未提取到有效点" << std::endl;
        return false;
    }

    // 2. 轨迹优化
    auto path = trajectoryOptimizer_->optimizePath(points);
    if (path.empty()) {
        std::cerr << "错误：轨迹优化失败" << std::endl;
        return false;
    }

    // 3. 生成RAPID代码（可选）
    generateRAPIDCode(points, path, "ABBDrawPicture", "ABBDrawPicture.prg");

    // 4. 执行机器人绘图
    if (robotDriver_ && robotDriver_->isConnect()) {
        return executeRobotDrawing(points, path, drawHeight);
    } else {
        std::cout << "机器人未连接，仅生成RAPID代码" << std::endl;
        return true;
    }
}

void ABBDrawController::setDrawingParameters(double speed, int zone, int acc) {
    drawSpeed_ = speed;
    drawZone_ = zone;
    drawAcc_ = acc;

    if (robotDriver_) {
        robotDriver_->setAcceleration(acc, 0);
    }
}

bool ABBDrawController::isRobotConnected() const {
    return robotDriver_ && robotDriver_->isConnect();
}

bool ABBDrawController::executeRobotDrawing(const std::vector<Point2D>& points,
                                           const std::vector<int>& path,
                                           double drawHeight) {
    std::cout << "开始机器人绘图..." << std::endl;

    // 移动到初始位置（抬笔状态）
    Point2D startPoint(0, 0);
    moveToPosition(startPoint, drawHeight + 0.2, false);

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 按照优化路径执行绘图
    for (size_t i = 1; i < path.size(); ++i) {
        const Point2D& currentPoint = points[path[i]];
        const Point2D& prevPoint = points[path[i-1]];

        double distance = TrajectoryOptimizer::calculateDistance(currentPoint, prevPoint);

        if (distance <= trajectoryOptimizer_->getDistanceThreshold()) {
            // 距离较近，落笔绘制
            moveToPosition(currentPoint, drawHeight, true);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        } else {
            // 距离较远，抬笔移动
            moveToPosition(prevPoint, drawHeight + liftHeight_, false);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            moveToPosition(currentPoint, drawHeight + liftHeight_, false);
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }

    // 返回初始位置
    moveToPosition(startPoint, drawHeight + 0.2, false);

    std::cout << "绘图完成" << std::endl;
    return true;
}

void ABBDrawController::moveToPosition(const Point2D& point, double height, bool isDrawing) {
    if (!robotDriver_) return;

    // 转换坐标系
    auto robotPose = convertToRobotCoordinates(point, height);

    // 执行移动
    robotDriver_->movel(robotPose, drawSpeed_, drawZone_, drawAcc_);
}

rw::math::Transform3D<> ABBDrawController::convertToRobotCoordinates(const Point2D& imagePoint, double height) {
    // 将图像坐标转换为机器人坐标
    // 这里需要根据实际的坐标系标定进行调整
    double robotX = imagePoint.y / 1000.0;  // 转换为米
    double robotY = imagePoint.x / 1000.0;  // 转换为米
    double robotZ = height;

    // 创建位置向量
    rw::math::Vector3D<> position(robotX, robotY, robotZ);

    // 创建旋转矩阵（保持工具垂直向下）
    rw::math::Rotation3D<> rotation = rw::math::Rotation3D<>::identity();

    return rw::math::Transform3D<>(position, rotation);
}

bool ABBDrawController::generateRAPIDCode(const std::vector<Point2D>& points,
                                         const std::vector<int>& path,
                                         const std::string& moduleName,
                                         const std::string& outputPath) {
    std::cout << "生成RAPID代码: " << outputPath << std::endl;

    std::ofstream file(outputPath);
    if (!file.is_open()) {
        std::cerr << "错误：无法创建RAPID文件 " << outputPath << std::endl;
        return false;
    }

    // 生成数组数据
    std::vector<std::string> arrayX, arrayY, arrayZ;

    for (size_t i = 0; i < path.size() - 1; ++i) {
        const Point2D& p1 = points[path[i]];
        const Point2D& p2 = points[path[i + 1]];

        double distance = TrajectoryOptimizer::calculateDistance(p1, p2);

        if (distance <= trajectoryOptimizer_->getDistanceThreshold()) {
            // 绘图状态
            arrayX.push_back(std::to_string(p1.x / 2.0));
            arrayY.push_back(std::to_string(p1.y / 2.0));
            arrayZ.push_back(std::to_string(-15.0));

            arrayX.push_back(std::to_string(p2.x / 2.0));
            arrayY.push_back(std::to_string(p2.y / 2.0));
            arrayZ.push_back(std::to_string(-15.0));
        } else {
            // 抬笔状态
            arrayX.push_back(std::to_string(p1.x / 2.0));
            arrayY.push_back(std::to_string(p1.y / 2.0));
            arrayZ.push_back(std::to_string(10.0));

            arrayX.push_back(std::to_string(p2.x / 2.0));
            arrayY.push_back(std::to_string(p2.y / 2.0));
            arrayZ.push_back(std::to_string(10.0));
        }
    }

    // 写入RAPID代码
    file << "%%%\n  VERSION:1\n  LANGUAGE:ENGLISH\n%%%\n\n";
    file << "MODULE " << moduleName << "\n\n";

    // 参考点和工具定义
    file << "! UPDATE reference point\n";
    file << "CONST robtarget P1:=[[639.4,-119.9,378.9],[0.13334, -0.23621, 0.96143, -0.04554],[0,-1,0,0],[9E+09,9E+09,9E+09,9E+09,9E+09,9E+09]];\n";
    file << "! UPDATE tool point\n";
    file << "PERS tooldata Bic:=[TRUE,[[0.500839,-0.574904,226.276],[1,0,0,0]],[0.25,[85,0,65],[1,0,0,0],0.01,0.01,0.01]];\n\n";

    // 数组定义
    file << "VAR num array_draw_x{" << arrayX.size() << "}:= [";
    for (size_t i = 0; i < arrayX.size(); ++i) {
        file << arrayX[i];
        if (i < arrayX.size() - 1) file << ", ";
    }
    file << "];\n";

    file << "VAR num array_draw_y{" << arrayY.size() << "}:= [";
    for (size_t i = 0; i < arrayY.size(); ++i) {
        file << arrayY[i];
        if (i < arrayY.size() - 1) file << ", ";
    }
    file << "];\n";

    file << "VAR num array_draw_z{" << arrayZ.size() << "}:= [";
    for (size_t i = 0; i < arrayZ.size(); ++i) {
        file << arrayZ[i];
        if (i < arrayZ.size() - 1) file << ", ";
    }
    file << "];\n\n";

    // 主程序
    file << "\tPROC main()\n";
    file << "\t\tMoveAbsJ [[45,0,0,0,90,0],[9E+09,9E+09,9E+09,9E+09,9E+09,9E+09]]\\NoEOffs,v100,z50,Bic;\n";
    file << "\t\tMoveJ Offs(P1,0,0,100),v100,fine,Bic;\n";
    file << "\t\tFOR i FROM 1 TO Dim(array_draw_x, 1) DO\n";
    file << "\t\t\tMoveL Offs(P1,array_draw_x{i},array_draw_y{i},array_draw_z{i}), v" << static_cast<int>(drawSpeed_) << ",z1,Bic;\n";
    file << "\t\tENDFOR\n";
    file << "\t\tMoveL Offs(P1,0,0,100),v" << static_cast<int>(drawSpeed_) << ",z10,Bic;\n";
    file << "\t\tWaitTime 2;\n";
    file << "\tENDPROC\n";
    file << "ENDMODULE\n";

    file.close();
    std::cout << "RAPID代码生成完成" << std::endl;
    return true;
}

} // namespace ABBDraw