{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/Testtest_twoaixsrobot.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/Testtest_twoaixsrobot.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "Test/test_twoaixsrobot/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicore/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/add_xerces-c.cmake", "builder/cmake/add_glog.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 9}, {"file": 3, "parent": 10}, {"command": 3, "file": 3, "line": 78, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 12}, {"command": 1, "file": 3, "line": 1, "parent": 11}, {"file": 2, "parent": 14}, {"command": 1, "file": 2, "line": 81, "parent": 15}, {"file": 9, "parent": 16}, {"command": 5, "file": 9, "line": 33, "parent": 17}, {"file": 8, "parent": 18}, {"command": 5, "file": 8, "line": 610, "parent": 19}, {"file": 7, "parent": 20}, {"command": 6, "file": 7, "line": 262, "parent": 21}, {"command": 5, "file": 7, "line": 141, "parent": 22}, {"file": 6, "parent": 23}, {"command": 1, "file": 6, "line": 53, "parent": 24}, {"file": 5, "parent": 25}, {"command": 4, "file": 5, "line": 101, "parent": 26}, {"command": 4, "file": 6, "line": 103, "parent": 24}, {"command": 6, "file": 7, "line": 262, "parent": 21}, {"command": 5, "file": 7, "line": 141, "parent": 29}, {"file": 10, "parent": 30}, {"command": 4, "file": 10, "line": 103, "parent": 31}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 33}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 35}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 37}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 39}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 41}, {"command": 3, "file": 3, "line": 84, "parent": 11}, {"command": 2, "file": 3, "line": 53, "parent": 43}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 45}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 47}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 49}, {"command": 1, "file": 2, "line": 81, "parent": 50}, {"file": 12, "parent": 51}, {"command": 5, "file": 12, "line": 18, "parent": 52}, {"file": 11, "parent": 53}, {"command": 7, "file": 11, "line": 310, "parent": 54}, {"command": 8, "file": 0, "line": 17, "parent": 2}, {"command": 8, "file": 0, "line": 13, "parent": 2}, {"command": 8, "file": 0, "line": 16, "parent": 2}, {"command": 8, "file": 0, "line": 14, "parent": 2}, {"command": 8, "file": 0, "line": 15, "parent": 2}, {"command": 9, "file": 2, "line": 54, "parent": 50}, {"command": 1, "file": 2, "line": 81, "parent": 50}, {"file": 13, "parent": 62}, {"command": 9, "file": 13, "line": 11, "parent": 63}, {"command": 1, "file": 2, "line": 81, "parent": 50}, {"file": 14, "parent": 65}, {"command": 9, "file": 14, "line": 16, "parent": 66}, {"command": 9, "file": 12, "line": 21, "parent": 52}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MD /O1 /Ob1 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 55, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 55, "define": "MSVC_AMD64"}, {"backtrace": 55, "define": "NOMINMAX"}, {"backtrace": 56, "define": "QT_CORE_LIB"}, {"backtrace": 57, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 58, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 59, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 60, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 55, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 55, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 55, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 55, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 55, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/build/Test/test_twoaixsrobot/Testtest_twoaixsrobot_autogen/include_MinSizeRel"}, {"backtrace": 61, "path": "D:/newfuxios/Test/test_twoaixsrobot/include"}, {"backtrace": 64, "path": "C:/opt/xerces-c/include"}, {"backtrace": 67, "path": "C:/opt/glog/include"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 68, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicore/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 68, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 69, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 69, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 69, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 70, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 70, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 71, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSerialPort"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd"}], "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /INCREMENTAL:NO /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicore\\MinSizeRel\\fuxicore.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 36, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 40, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Network_conda.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5SerialPort_conda.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Testtest_twoaixsrobot", "nameOnDisk": "Testtest_twoaixsrobot.exe", "paths": {"build": "Test/test_twoaixsrobot", "source": "Test/test_twoaixsrobot"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Test/test_twoaixsrobot/Testtest_twoaixsrobot_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_twoaixsrobot/src/PouringRobotController.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "Test/test_twoaixsrobot/src/httplib.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}