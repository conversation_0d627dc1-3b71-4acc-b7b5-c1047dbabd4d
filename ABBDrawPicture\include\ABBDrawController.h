#pragma once

#include "ImageProcessor.h"
#include "TrajectoryOptimizer.h"
#include "AbbRobotDriver.h"
#include <memory>
#include <string>
#include <rw/math.hpp>

namespace ABBDraw {

/**
 * @brief ABB机器人绘图控制器
 */
class ABBDrawController {
public:
    using Point2D = ImageProcessor::Point2D;

    /**
     * @brief 构造函数
     */
    ABBDrawController();

    /**
     * @brief 析构函数
     */
    ~ABBDrawController();

    /**
     * @brief 初始化控制器
     * @param robotIP 机器人IP地址
     * @param robotPort 机器人端口
     * @return 是否成功
     */
    bool initialize(const std::string& robotIP = "*************", int robotPort = 7000);

    /**
     * @brief 执行绘图任务
     * @param imagePath 图像文件路径
     * @param scaleFactor 缩放因子
     * @param targetSize 目标尺寸
     * @param drawHeight 绘图高度（Z坐标）
     * @return 是否成功
     */
    bool executeDrawing(const std::string& imagePath,
                       double scaleFactor = 1.0,
                       int targetSize = 350,
                       double drawHeight = 0.1065);

    /**
     * @brief 设置绘图参数
     * @param speed 移动速度
     * @param zone 交融半径
     * @param acc 加速度
     */
    void setDrawingParameters(double speed = 5.0, int zone = 1, int acc = 100);

    /**
     * @brief 生成RAPID代码
     * @param points 点集
     * @param path 路径索引
     * @param moduleName 模块名称
     * @param outputPath 输出文件路径
     * @return 是否成功
     */
    bool generateRAPIDCode(const std::vector<Point2D>& points,
                          const std::vector<int>& path,
                          const std::string& moduleName = "ABBDraw",
                          const std::string& outputPath = "ABBDraw.prg");

    /**
     * @brief 获取机器人连接状态
     */
    bool isRobotConnected() const;

private:
    std::unique_ptr<ImageProcessor> imageProcessor_;
    std::unique_ptr<TrajectoryOptimizer> trajectoryOptimizer_;
    std::unique_ptr<AbbRobotDriverEgmUpdate> robotDriver_;

    // 绘图参数
    double drawSpeed_;
    int drawZone_;
    int drawAcc_;
    double liftHeight_;  // 抬笔高度

    /**
     * @brief 执行机器人绘图动作
     * @param points 点集
     * @param path 路径索引
     * @param drawHeight 绘图高度
     * @return 是否成功
     */
    bool executeRobotDrawing(const std::vector<Point2D>& points,
                            const std::vector<int>& path,
                            double drawHeight);

    /**
     * @brief 移动到指定位置
     * @param point 目标点
     * @param height 高度
     * @param isDrawing 是否为绘图状态
     */
    void moveToPosition(const Point2D& point, double height, bool isDrawing = true);

    /**
     * @brief 转换坐标系（图像坐标到机器人坐标）
     * @param imagePoint 图像坐标点
     * @return 机器人坐标点
     */
    rw::math::Transform3D<> convertToRobotCoordinates(const Point2D& imagePoint, double height);
};

} // namespace ABBDraw