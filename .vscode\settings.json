{"files.associations": {"exception": "cpp", "functional": "cpp", "future": "cpp", "memory": "cpp", "optional": "cpp", "any": "cpp", "filesystem": "cpp", "variant": "cpp", "xstring": "cpp", "atomic": "cpp", "charconv": "cpp", "deque": "cpp", "forward_list": "cpp", "fstream": "cpp", "list": "cpp", "memory_resource": "cpp", "random": "cpp", "regex": "cpp", "sstream": "cpp", "streambuf": "cpp", "strstream": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "algorithm": "cpp", "bit": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "format": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "ratio": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "string": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xstddef": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "array": "cpp", "bitset": "cpp", "cfenv": "cpp", "complex": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cwctype": "cpp", "numeric": "cpp", "queue": "cpp", "set": "cpp", "shared_mutex": "cpp", "span": "cpp", "stack": "cpp", "typeindex": "cpp", "unordered_set": "cpp", "valarray": "cpp", "codecvt": "cpp", "ranges": "cpp", "latch": "cpp"}, "kiroAgent.configureMCP": "Disabled"}