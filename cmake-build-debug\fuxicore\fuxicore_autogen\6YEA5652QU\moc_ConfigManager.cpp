/****************************************************************************
** Meta object code from reading C++ file 'ConfigManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../fuxicore/include/ConfigManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConfigManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_Fuxi__Core__ConfigManager_t {
    QByteArrayData data[20];
    char stringdata0[237];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Fuxi__Core__ConfigManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Fuxi__Core__ConfigManager_t qt_meta_stringdata_Fuxi__Core__ConfigManager = {
    {
QT_MOC_LITERAL(0, 0, 25), // "Fuxi::Core::ConfigManager"
QT_MOC_LITERAL(1, 26, 12), // "configLoaded"
QT_MOC_LITERAL(2, 39, 0), // ""
QT_MOC_LITERAL(3, 40, 8), // "filePath"
QT_MOC_LITERAL(4, 49, 11), // "configSaved"
QT_MOC_LITERAL(5, 61, 14), // "configReloaded"
QT_MOC_LITERAL(6, 76, 13), // "configChanged"
QT_MOC_LITERAL(7, 90, 3), // "key"
QT_MOC_LITERAL(8, 94, 8), // "oldValue"
QT_MOC_LITERAL(9, 103, 8), // "newValue"
QT_MOC_LITERAL(10, 112, 17), // "configFileChanged"
QT_MOC_LITERAL(11, 130, 16), // "validationFailed"
QT_MOC_LITERAL(12, 147, 16), // "ValidationResult"
QT_MOC_LITERAL(13, 164, 6), // "result"
QT_MOC_LITERAL(14, 171, 7), // "message"
QT_MOC_LITERAL(15, 179, 13), // "backupCreated"
QT_MOC_LITERAL(16, 193, 10), // "backupPath"
QT_MOC_LITERAL(17, 204, 13), // "onFileChanged"
QT_MOC_LITERAL(18, 218, 4), // "path"
QT_MOC_LITERAL(19, 223, 13) // "onReloadTimer"

    },
    "Fuxi::Core::ConfigManager\0configLoaded\0"
    "\0filePath\0configSaved\0configReloaded\0"
    "configChanged\0key\0oldValue\0newValue\0"
    "configFileChanged\0validationFailed\0"
    "ValidationResult\0result\0message\0"
    "backupCreated\0backupPath\0onFileChanged\0"
    "path\0onReloadTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Fuxi__Core__ConfigManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   59,    2, 0x06 /* Public */,
       4,    1,   62,    2, 0x06 /* Public */,
       5,    0,   65,    2, 0x06 /* Public */,
       6,    3,   66,    2, 0x06 /* Public */,
      10,    1,   73,    2, 0x06 /* Public */,
      11,    2,   76,    2, 0x06 /* Public */,
      15,    1,   81,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      17,    1,   84,    2, 0x08 /* Private */,
      19,    0,   87,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QVariant, QMetaType::QVariant,    7,    8,    9,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, 0x80000000 | 12, QMetaType::QString,   13,   14,
    QMetaType::Void, QMetaType::QString,   16,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,   18,
    QMetaType::Void,

       0        // eod
};

void Fuxi::Core::ConfigManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConfigManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->configLoaded((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->configSaved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->configReloaded(); break;
        case 3: _t->configChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QVariant(*)>(_a[2])),(*reinterpret_cast< const QVariant(*)>(_a[3]))); break;
        case 4: _t->configFileChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->validationFailed((*reinterpret_cast< ValidationResult(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->backupCreated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onFileChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->onReloadTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConfigManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::configLoaded)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::configSaved)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::configReloaded)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)(const QString & , const QVariant & , const QVariant & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::configChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::configFileChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)(ValidationResult , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::validationFailed)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ConfigManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConfigManager::backupCreated)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject Fuxi::Core::ConfigManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_Fuxi__Core__ConfigManager.data,
    qt_meta_data_Fuxi__Core__ConfigManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *Fuxi::Core::ConfigManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Fuxi::Core::ConfigManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Fuxi__Core__ConfigManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Fuxi::Core::ConfigManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void Fuxi::Core::ConfigManager::configLoaded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void Fuxi::Core::ConfigManager::configSaved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void Fuxi::Core::ConfigManager::configReloaded()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void Fuxi::Core::ConfigManager::configChanged(const QString & _t1, const QVariant & _t2, const QVariant & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void Fuxi::Core::ConfigManager::configFileChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void Fuxi::Core::ConfigManager::validationFailed(ValidationResult _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void Fuxi::Core::ConfigManager::backupCreated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
