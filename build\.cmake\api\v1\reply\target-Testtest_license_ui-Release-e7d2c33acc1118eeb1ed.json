{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/Release/Testtest_license_ui.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/Release/Testtest_license_ui.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "Test/test_license_ui/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicore/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "builder/cmake/add_glog.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 24, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 78, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 14}, {"command": 1, "file": 3, "line": 1, "parent": 13}, {"file": 2, "parent": 16}, {"command": 1, "file": 2, "line": 81, "parent": 17}, {"file": 8, "parent": 18}, {"command": 5, "file": 8, "line": 33, "parent": 19}, {"file": 7, "parent": 20}, {"command": 5, "file": 7, "line": 610, "parent": 21}, {"file": 6, "parent": 22}, {"command": 6, "file": 6, "line": 262, "parent": 23}, {"command": 5, "file": 6, "line": 141, "parent": 24}, {"file": 5, "parent": 25}, {"command": 4, "file": 5, "line": 103, "parent": 26}, {"command": 6, "file": 6, "line": 262, "parent": 23}, {"command": 5, "file": 6, "line": 141, "parent": 28}, {"file": 9, "parent": 29}, {"command": 4, "file": 9, "line": 103, "parent": 30}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 32}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 34}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 36}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 40}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 42}, {"file": 10}, {"command": 1, "file": 10, "line": 3, "parent": 44}, {"file": 3, "parent": 45}, {"command": 3, "file": 3, "line": 78, "parent": 46}, {"command": 2, "file": 3, "line": 53, "parent": 47}, {"command": 1, "file": 5, "line": 53, "parent": 26}, {"file": 11, "parent": 49}, {"command": 4, "file": 11, "line": 101, "parent": 50}, {"command": 1, "file": 3, "line": 1, "parent": 46}, {"file": 2, "parent": 52}, {"command": 1, "file": 2, "line": 81, "parent": 53}, {"file": 8, "parent": 54}, {"command": 5, "file": 8, "line": 33, "parent": 55}, {"file": 7, "parent": 56}, {"command": 5, "file": 7, "line": 610, "parent": 57}, {"file": 6, "parent": 58}, {"command": 6, "file": 6, "line": 262, "parent": 59}, {"command": 5, "file": 6, "line": 141, "parent": 60}, {"file": 5, "parent": 61}, {"command": 4, "file": 5, "line": 103, "parent": 62}, {"command": 6, "file": 6, "line": 262, "parent": 59}, {"command": 5, "file": 6, "line": 141, "parent": 64}, {"file": 9, "parent": 65}, {"command": 4, "file": 9, "line": 103, "parent": 66}, {"command": 3, "file": 3, "line": 84, "parent": 46}, {"command": 2, "file": 3, "line": 53, "parent": 68}, {"command": 3, "file": 3, "line": 84, "parent": 46}, {"command": 2, "file": 3, "line": 53, "parent": 70}, {"command": 3, "file": 3, "line": 84, "parent": 46}, {"command": 2, "file": 3, "line": 53, "parent": 72}, {"command": 3, "file": 3, "line": 84, "parent": 46}, {"command": 2, "file": 3, "line": 53, "parent": 74}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 76}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 78}, {"command": 7, "file": 0, "line": 17, "parent": 2}, {"command": 7, "file": 0, "line": 13, "parent": 2}, {"command": 7, "file": 0, "line": 16, "parent": 2}, {"command": 7, "file": 0, "line": 14, "parent": 2}, {"command": 7, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 85}, {"command": 8, "file": 2, "line": 54, "parent": 86}, {"command": 1, "file": 2, "line": 81, "parent": 86}, {"file": 12, "parent": 88}, {"command": 8, "file": 12, "line": 16, "parent": 89}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 80, "define": "QT_CORE_LIB"}, {"backtrace": 81, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 82, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 83, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 9, "define": "QT_TESTCASE_BUILDDIR=\"D:/newfuxios/build\""}, {"backtrace": 9, "define": "QT_TESTLIB_LIB"}, {"backtrace": 84, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/build/Test/test_license_ui/Testtest_license_ui_autogen/include_Release"}, {"backtrace": 87, "path": "D:/newfuxios/Test/test_license_ui/include"}, {"backtrace": 90, "path": "C:/opt/glog/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicore/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 91, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 91, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 91, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 92, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 92, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 93, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}, {"backtrace": 94, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtTest"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSerialPort"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd"}], "id": "Testtest_license_ui::@bb78083dcad0a236858d", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                             /INCREMENTAL:NO /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicore\\Release\\fuxicore.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\fuxicommon\\Release\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Test_conda.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 37, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Network_conda.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5SerialPort_conda.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 63, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 67, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 69, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 71, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 77, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 79, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Testtest_license_ui", "nameOnDisk": "Testtest_license_ui.exe", "paths": {"build": "Test/test_license_ui", "source": "Test/test_license_ui"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Test/test_license_ui/Testtest_license_ui_autogen/mocs_compilation_Release.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_license_ui/src/LicenseManagerUI.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Test/test_license_ui/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "Test/test_license_ui/include/LicenseManagerUI.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}