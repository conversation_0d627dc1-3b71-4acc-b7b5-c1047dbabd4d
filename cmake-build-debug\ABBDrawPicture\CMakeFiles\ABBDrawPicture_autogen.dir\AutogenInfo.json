{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "D:/newfuxios/cmake-build-debug/ABBDrawPicture/ABBDrawPicture_autogen", "CMAKE_BINARY_DIR": "D:/newfuxios/cmake-build-debug", "CMAKE_CURRENT_BINARY_DIR": "D:/newfuxios/cmake-build-debug/ABBDrawPicture", "CMAKE_CURRENT_SOURCE_DIR": "D:/newfuxios/ABBDrawPicture", "CMAKE_EXECUTABLE": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe", "CMAKE_LIST_FILES": ["D:/newfuxios/ABBDrawPicture/CMakeLists.txt", "D:/newfuxios/builder/cmake/library.cmake", "D:/newfuxios/builder/cmake/common.cmake", "D:/newfuxios/builder/cmake/add_glog.cmake", "D:/newfuxios/builder/cmake/add_rttr.cmake", "C:/opt/rttr/cmake/rttr-config-version.cmake", "C:/opt/rttr/cmake/rttr-config.cmake", "C:/opt/rttr/cmake/rttr-config-debug.cmake", "C:/opt/rttr/cmake/rttr-config-version.cmake", "D:/newfuxios/builder/cmake/add_openssl.cmake", "D:/newfuxios/builder/cmake/add_boost.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfigVersion.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/boost_system-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/boost_system-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/libboost_system-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/boost_atomic-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/boost_atomic-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/boost_date_time-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/boost_date_time-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/boost_iostreams-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/boost_iostreams-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/boost_serialization-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/boost_serialization-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/libboost_thread-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/boost_chrono-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/boost_chrono-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckLibraryExists.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckIncludeFile.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/boost_program_options-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/boost_program_options-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "D:/newfuxios/builder/cmake/add_xerces-c.cmake", "D:/newfuxios/builder/cmake/add_opencv.cmake", "C:/opt/opencv/build/OpenCVConfig-version.cmake", "C:/opt/opencv/build/OpenCVConfig.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeParseArguments.cmake"], "CMAKE_SOURCE_DIR": "D:/newfuxios", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["D:/newfuxios/ABBDrawPicture/include/ABBDrawController.h", "MU", "6YEA5652QU/moc_ABBDrawController.cpp", null], ["D:/newfuxios/ABBDrawPicture/include/ImageProcessor.h", "MU", "6YEA5652QU/moc_ImageProcessor.cpp", null], ["D:/newfuxios/ABBDrawPicture/include/TrajectoryOptimizer.h", "MU", "6YEA5652QU/moc_TrajectoryOptimizer.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "D:/newfuxios/cmake-build-debug/ABBDrawPicture/ABBDrawPicture_autogen/include", "MOC_COMPILATION_FILE": "D:/newfuxios/cmake-build-debug/ABBDrawPicture/ABBDrawPicture_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "CPPHTTPLIB_OPENSSL_SUPPORT", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["D:/newfuxios/ABBDrawPicture/include", "C:/opt/glog/include", "C:/opt/openssl/include", "C:/opt/xerces-c/include", "C:/opt/opencv/build/include", "C:/opt/rttr/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 12, "PARSE_CACHE_FILE": "D:/newfuxios/cmake-build-debug/ABBDrawPicture/CMakeFiles/ABBDrawPicture_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "D:/newfuxios/cmake-build-debug/ABBDrawPicture/CMakeFiles/ABBDrawPicture_autogen.dir/AutogenUsed.txt", "SOURCES": [["D:/newfuxios/ABBDrawPicture/src/ABBDrawController.cpp", "MU", null], ["D:/newfuxios/ABBDrawPicture/src/ImageProcessor.cpp", "MU", null], ["D:/newfuxios/ABBDrawPicture/src/TrajectoryOptimizer.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}