#include "ImageProcessor.h"
#include <iostream>
#include <algorithm>

namespace ABBDraw {

ImageProcessor::ImageProcessor() {
    // 构造函数实现
}

ImageProcessor::~ImageProcessor() {
    // 析构函数实现
}

std::vector<ImageProcessor::Point2D> ImageProcessor::processImage(const std::string& imagePath,
                                                                 double scaleFactor,
                                                                 int targetSize) {
    std::cout << "开始处理图像: " << imagePath << std::endl;

    // 读取图像
    originalImage_ = cv::imread(imagePath);
    if (originalImage_.empty()) {
        std::cerr << "错误：无法读取图像文件 " << imagePath << std::endl;
        return {};
    }

    // 调整图像尺寸
    processedImage_ = resizeImage(originalImage_, targetSize);
    processedSize_ = processedImage_.size();

    // 转换为灰度图像
    cv::Mat grayImage;
    cv::cvtColor(processedImage_, grayImage, cv::COLOR_BGR2GRAY);

    // 边缘检测
    edgeImage_ = detectEdges(grayImage);

    // 显示处理结果
    cv::imshow("原始图像", processedImage_);
    cv::imshow("边缘检测", edgeImage_);

    // 提取边缘点
    std::vector<Point2D> points = extractPoints(edgeImage_, scaleFactor);

    std::cout << "图像处理完成，提取到 " << points.size() << " 个边缘点" << std::endl;

    return points;
}

cv::Mat ImageProcessor::resizeImage(const cv::Mat& image, int targetSize) {
    int rows = image.rows;
    int cols = image.cols;
    int maxDim = std::max(rows, cols);

    // 计算缩放比例
    double scaleX = static_cast<double>(targetSize * cols) / (maxDim * cols);
    double scaleY = static_cast<double>(targetSize * rows) / (maxDim * rows);

    cv::Mat resizedImage;
    cv::resize(image, resizedImage, cv::Size(0, 0), scaleX, scaleY, cv::INTER_CUBIC);

    return resizedImage;
}

cv::Mat ImageProcessor::detectEdges(const cv::Mat& grayImage) {
    cv::Mat edges;
    cv::Canny(grayImage, edges, 0, 255, 3);
    return edges;
}

std::vector<ImageProcessor::Point2D> ImageProcessor::extractPoints(const cv::Mat& edgeImage, double scaleFactor) {
    std::vector<Point2D> points;

    for (int row = 1; row < edgeImage.rows - 1; ++row) {
        for (int col = 1; col < edgeImage.cols - 1; ++col) {
            if (edgeImage.at<uchar>(row, col) == 255) {
                points.emplace_back(col * scaleFactor, row * scaleFactor);
            }
        }
    }

    return points;
}

void ImageProcessor::showResult() const {
    if (!processedImage_.empty()) {
        cv::imshow("处理后图像", processedImage_);
    }
    if (!edgeImage_.empty()) {
        cv::imshow("边缘检测结果", edgeImage_);
    }
    cv::waitKey(1);
}

} // namespace ABBDraw