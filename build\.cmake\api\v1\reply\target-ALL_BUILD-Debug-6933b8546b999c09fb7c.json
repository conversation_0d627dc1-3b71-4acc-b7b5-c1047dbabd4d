{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["Test/test_license_manager/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"id": "Testtest_license_manager::@7a0ade4671e16056f257"}], "id": "ALL_BUILD::@7a0ade4671e16056f257", "isGeneratorProvided": true, "name": "ALL_BUILD", "paths": {"build": "Test/test_license_manager", "source": "Test/test_license_manager"}, "sources": [], "type": "UTILITY"}