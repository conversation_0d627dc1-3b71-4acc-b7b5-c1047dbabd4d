#include "TrajectoryOptimizer.h"
#include <iostream>
#include <cmath>
#include <algorithm>
#include <limits>

namespace ABBDraw {

TrajectoryOptimizer::TrajectoryOptimizer() : distanceThreshold_(2.0) {
    // 构造函数实现
}

TrajectoryOptimizer::~TrajectoryOptimizer() {
    // 析构函数实现
}

std::vector<int> TrajectoryOptimizer::optimizePath(const std::vector<Point2D>& points) {
    if (points.empty()) {
        std::cerr << "错误：点集为空" << std::endl;
        return {};
    }

    std::cout << "开始优化轨迹，点数: " << points.size() << std::endl;

    // 计算距离矩阵
    auto distanceMatrix = calculateDistanceMatrix(points);
    std::cout << "距离矩阵计算完成" << std::endl;

    // 使用最近邻算法求解TSP
    std::cout << "开始求解TSP问题" << std::endl;
    auto path = nearestNeighborTSP(distanceMatrix);
    std::cout << "TSP求解完成" << std::endl;

    return path;
}

double TrajectoryOptimizer::calculateDistance(const Point2D& p1, const Point2D& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}

std::vector<std::vector<double>> TrajectoryOptimizer::calculateDistanceMatrix(const std::vector<Point2D>& points) {
    size_t n = points.size();
    std::vector<std::vector<double>> matrix(n, std::vector<double>(n, 0.0));

    for (size_t i = 0; i < n; ++i) {
        for (size_t j = 0; j < n; ++j) {
            if (i != j) {
                matrix[i][j] = calculateDistance(points[i], points[j]);
            }
        }
    }

    return matrix;
}

std::vector<int> TrajectoryOptimizer::solveTSPGreedy(const std::vector<std::vector<double>>& distanceMatrix) {
    // 简单的贪心算法实现
    return nearestNeighborTSP(distanceMatrix, 0);
}

std::vector<int> TrajectoryOptimizer::nearestNeighborTSP(const std::vector<std::vector<double>>& distanceMatrix, int startIndex) {
    size_t n = distanceMatrix.size();
    if (n == 0) return {};

    std::vector<int> path;
    std::vector<bool> visited(n, false);

    int currentCity = startIndex;
    path.push_back(currentCity);
    visited[currentCity] = true;

    for (size_t i = 1; i < n; ++i) {
        double minDistance = std::numeric_limits<double>::max();
        int nextCity = -1;

        for (size_t j = 0; j < n; ++j) {
            if (!visited[j] && distanceMatrix[currentCity][j] < minDistance) {
                minDistance = distanceMatrix[currentCity][j];
                nextCity = static_cast<int>(j);
            }
        }

        if (nextCity != -1) {
            path.push_back(nextCity);
            visited[nextCity] = true;
            currentCity = nextCity;
        }
    }

    return path;
}

} // namespace ABBDraw