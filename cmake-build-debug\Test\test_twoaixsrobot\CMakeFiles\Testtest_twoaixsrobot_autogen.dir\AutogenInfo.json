{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot/Testtest_twoaixsrobot_autogen", "CMAKE_BINARY_DIR": "D:/newfuxios/cmake-build-debug", "CMAKE_CURRENT_BINARY_DIR": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot", "CMAKE_CURRENT_SOURCE_DIR": "D:/newfuxios/Test/test_twoaixsrobot", "CMAKE_EXECUTABLE": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe", "CMAKE_LIST_FILES": ["D:/newfuxios/Test/test_twoaixsrobot/CMakeLists.txt", "D:/newfuxios/builder/cmake/executable.cmake", "D:/newfuxios/builder/cmake/common.cmake", "D:/newfuxios/builder/cmake/add_xerces-c.cmake", "D:/newfuxios/builder/cmake/add_glog.cmake", "D:/newfuxios/builder/cmake/add_robwork.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfigVersion.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkTargets.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkTargets-debug.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkBuildConfig_debug.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "D:/newfuxios/builder/cmake/qt_deploy.cmake"], "CMAKE_SOURCE_DIR": "D:/newfuxios", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["D:/newfuxios/Test/test_twoaixsrobot/src/httplib.h", "MU", "UVLADIE3JM/moc_httplib.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot/Testtest_twoaixsrobot_autogen/include", "MOC_COMPILATION_FILE": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot/Testtest_twoaixsrobot_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["BIND_FORTRAN_LOWERCASE_UNDERSCORE", "BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "MSVC_AMD64", "NOMINMAX", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32", "WIN32_LEAN_AND_MEAN", "_CRT_SECURE_NO_DEPRECATE", "_CRT_SECURE_NO_WARNINGS", "_SCL_SECURE_NO_WARNINGS", "_WIN32_WINNT=0x0501"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["D:/newfuxios/Test/test_twoaixsrobot/include", "C:/opt/xerces-c/include", "C:/opt/glog/include", "C:/opt/robwork-21.12/robwork-21.12/ext/eigen3", "C:/opt/robwork-21.12/include/robwork-21.12", "C:/opt/robwork-21.12/robwork-21.12/ext/rwyaobi", "C:/opt/robwork-21.12/robwork-21.12/ext/rwpqp", "C:/opt/robwork-21.12/robwork-21.12/ext/qhull/src", "C:/opt/robwork-21.12/robwork-21.12/ext/csgjs/src", "C:/opt/robwork-21.12/robwork-21.12/ext/zlib", "C:/opt/robwork-21.12/robwork-21.12/ext/fcl/fcl/include", "C:/opt/robwork-21.12/robwork-21.12/ext/assimp/include", "C:/opt/robwork-21.12/robwork-21.12/ext", "D:/newfuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/opt/rttr/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 12, "PARSE_CACHE_FILE": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot/CMakeFiles/Testtest_twoaixsrobot_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot/CMakeFiles/Testtest_twoaixsrobot_autogen.dir/AutogenUsed.txt", "SOURCES": [["D:/newfuxios/Test/test_twoaixsrobot/src/PouringRobotController.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}