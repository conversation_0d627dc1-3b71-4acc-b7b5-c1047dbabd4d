{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["tool/handeyecalui/handeyecalui/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"backtrace": 0, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 0, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}, {"backtrace": 0, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}], "id": "toolhandeyecaluihandeyecalui_autogen::@64c63141ea1fe7a116f6", "isGeneratorProvided": true, "name": "toolhandeyecaluihandeyecalui_autogen", "paths": {"build": "tool/handeyecalui/handeyecalui", "source": "tool/handeyecalui/handeyecalui"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}