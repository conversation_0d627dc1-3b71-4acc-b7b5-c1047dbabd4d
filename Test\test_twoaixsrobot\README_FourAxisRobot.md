# 四轴机器人倾倒控制器

## 概述
这个项目已经从两轴机器人控制器升级为四轴机器人控制器，使用新的机器人配置文件 `fourRobot.xml`。

## 机器人配置

### 四轴机器人关节配置
- **J1 (左右移动)**: Prismatic关节，范围 0 到 0.3 米
- **J2 (上下移动)**: Prismatic关节，范围 -0.2 到 0 米
- **J3 (倾倒旋转)**: Revolute关节，范围 -180 到 0 度
  - 0度: 完全倾倒，瓶口向下
  - -180度: 瓶口抬起
- **J4 (旋转料瓶)**: Revolute关节，范围 -360 到 360 度

### 配置文件
- 机器人配置: `"C:\Users\<USER>\Desktop\updateload\fourRobot.xml"`
- 烧杯位置: (0.140, 0.370, 0.328) 米
- 初始/Home位置: (0, 0, -180°, 0) - 瓶口完全抬起状态

## 使用方法

### 编译
```bash
cd Test/test_twoaixsrobot
mkdir build && cd build
cmake ..
make
```

### 运行
```bash
./PouringRobotController [workcell_file] [server_host] [server_port]
```

默认参数:
- workcell_file: `Test/test_twoaixsrobot/data/fourRobot/fourRobot.xml`
- server_host: `localhost`
- server_port: `8080`

### 控制命令

#### 基本移动命令
- `move4`: 四轴移动 - 输入四个参数 (左右位置 上下位置 倾倒角度 瓶子旋转角度)
- `tilt`: 倾倒动作 - 输入倾倒角度 (-180到0度)
- `rotate`: 瓶子旋转 - 输入旋转角度 (-360到360度)
- `home`: 回到原点位置 (J1=0, J2=0, J3=-180°, J4=0)

#### 兼容性命令
- `move <linear_pos> <rotation_angle>`: 兼容两轴模式的移动命令
- `pour <angle>`: 倾倒操作

#### 自动倾倒命令
- `pidpour <target_weight>`: PID控制的自动倾倒
- `multistage <target_weight>`: 多阶段倾倒
- `sequence`: 执行预设的倾倒序列

#### 其他命令
- `test`: 测试服务器连接
- `quit`: 退出程序

## 示例使用

### 基本四轴移动
```
move4
输入四轴位置 (左右 上下 倾倒角度 瓶子旋转角度): 0.15 -0.1 -30 45
```

### 倾倒操作
```
tilt
输入倾倒角度 (-180到0度): -45
```

### 瓶子旋转
```
rotate  
输入瓶子旋转角度 (-360到360度): 90
```

### 自动倾倒
```
pidpour 50
```
这将自动倾倒直到达到50克的目标重量。

## 技术细节

### 坐标系统
- J1 (左右): 正值向右移动
- J2 (上下): 负值向下移动
- J3 (倾倒): 负值为倾倒动作，0度为完全倾倒
- J4 (旋转): 正值为顺时针旋转

### TCP补偿
控制器会自动计算TCP偏移以保持倾倒点在烧杯中心上方，即使在倾倒过程中也能保持精确定位。

### 安全限制
所有关节移动都会检查关节限制，超出范围的命令会被自动限制到安全范围内。

## 注意事项
1. 确保机器人配置文件路径正确
2. 倾倒角度 -90度表示完全倾倒，使用时要小心
3. 瓶子旋转功能可以用于混合或调整倾倒方向
4. 建议先使用小角度测试，确认机器人响应正常后再进行大幅度动作
