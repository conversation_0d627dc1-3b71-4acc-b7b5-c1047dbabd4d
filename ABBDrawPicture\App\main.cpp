#include "ABBDrawController.h"
#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>

using namespace ABBDraw;

void printUsage() {
    std::cout << "ABB机器人绘图程序使用说明:" << std::endl;
    std::cout << "用法: ABBDrawPicture [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -i, --image <路径>     输入图像文件路径 (默认: test_image.jpg)" << std::endl;
    std::cout << "  -s, --scale <因子>     缩放因子 (默认: 1.0)" << std::endl;
    std::cout << "  -t, --target <尺寸>    目标尺寸 (默认: 350)" << std::endl;
    std::cout << "  -r, --robot <IP>       机器人IP地址 (默认: *************)" << std::endl;
    std::cout << "  -p, --port <端口>      机器人端口 (默认: 7000)" << std::endl;
    std::cout << "  -h, --height <高度>    绘图高度 (默认: 0.1065)" << std::endl;
    std::cout << "  --help                 显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  ABBDrawPicture -i image.jpg -s 1.0 -t 300" << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "=== ABB机器人绘图系统 ===" << std::endl;
    std::cout << "基于图像处理和轨迹优化的机器人绘图解决方案" << std::endl;
    std::cout << "适用于ABB机器人的大规模定制/个性化产品生产" << std::endl;
    std::cout << std::endl;

    // 默认参数
    std::string imagePath = "test_image.jpg";
    double scaleFactor = 1.0;
    int targetSize = 350;
    std::string robotIP = "*************";
    int robotPort = 7000;
    double drawHeight = 0.1065;

    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "--help") {
            printUsage();
            return 0;
        } else if ((arg == "-i" || arg == "--image") && i + 1 < argc) {
            imagePath = argv[++i];
        } else if ((arg == "-s" || arg == "--scale") && i + 1 < argc) {
            scaleFactor = std::stod(argv[++i]);
        } else if ((arg == "-t" || arg == "--target") && i + 1 < argc) {
            targetSize = std::stoi(argv[++i]);
        } else if ((arg == "-r" || arg == "--robot") && i + 1 < argc) {
            robotIP = argv[++i];
        } else if ((arg == "-p" || arg == "--port") && i + 1 < argc) {
            robotPort = std::stoi(argv[++i]);
        } else if ((arg == "-h" || arg == "--height") && i + 1 < argc) {
            drawHeight = std::stod(argv[++i]);
        }
    }

    // 显示配置信息
    std::cout << "配置参数:" << std::endl;
    std::cout << "  图像文件: " << imagePath << std::endl;
    std::cout << "  缩放因子: " << scaleFactor << std::endl;
    std::cout << "  目标尺寸: " << targetSize << std::endl;
    std::cout << "  机器人IP: " << robotIP << std::endl;
    std::cout << "  机器人端口: " << robotPort << std::endl;
    std::cout << "  绘图高度: " << drawHeight << std::endl;
    std::cout << std::endl;

    try {
        // 创建控制器
        ABBDrawController controller;

        // 设置绘图参数
        controller.setDrawingParameters(5.0, 1, 100);

        // 初始化机器人连接
        std::cout << "正在连接机器人..." << std::endl;
        bool robotConnected = controller.initialize(robotIP, robotPort);

        if (!robotConnected) {
            std::cout << "警告：机器人连接失败，将仅生成RAPID代码" << std::endl;
        }

        // 执行绘图任务
        std::cout << "开始执行绘图任务..." << std::endl;
        bool success = controller.executeDrawing(imagePath, scaleFactor, targetSize, drawHeight);

        if (success) {
            std::cout << "绘图任务执行成功！" << std::endl;
            std::cout << "RAPID代码已生成到: ABBDrawPicture.prg" << std::endl;

            if (robotConnected) {
                std::cout << "机器人绘图已完成" << std::endl;
            }
        } else {
            std::cerr << "绘图任务执行失败" << std::endl;
            return 1;
        }

        // 等待用户按键退出
        std::cout << "按任意键退出..." << std::endl;
        cv::waitKey(0);
        cv::destroyAllWindows();

    } catch (const std::exception& e) {
        std::cerr << "程序执行出错: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "程序执行完成" << std::endl;
    return 0;
}