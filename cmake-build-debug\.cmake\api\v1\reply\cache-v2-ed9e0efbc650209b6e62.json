{"entries": [{"name": "ABBDrawPicture_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/ABBDrawPicture"}, {"name": "ABBDrawPicture_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "ABBDrawPicture_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/ABBDrawPicture"}, {"name": "Analysis_RobotApp_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/App"}, {"name": "Analysis_RobotApp_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotApp_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/App"}, {"name": "Analysis_RobotalgorithmscoordinateTransform_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/coordinateTransform"}, {"name": "Analysis_RobotalgorithmscoordinateTransform_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotalgorithmscoordinateTransform_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/algorithms/coordinateTransform"}, {"name": "Analysis_RobotalgorithmspouringControl_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/pouringControl"}, {"name": "Analysis_RobotalgorithmspouringControl_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotalgorithmspouringControl_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/algorithms/pouringControl"}, {"name": "Analysis_RobotalgorithmstcpPositionMaintain_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/tcpPositionMaintain"}, {"name": "Analysis_RobotalgorithmstcpPositionMaintain_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotalgorithmstcpPositionMaintain_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/algorithms/tcpPositionMaintain"}, {"name": "Analysis_RobotdriversaixsDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/aixsDriver"}, {"name": "Analysis_RobotdriversaixsDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversaixsDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/aixsDriver"}, {"name": "Analysis_RobotdriversbalanceDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/balanceDriver"}, {"name": "Analysis_RobotdriversbalanceDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversbalanceDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/balanceDriver"}, {"name": "Analysis_RobotdriversheatingMagneticStirrerDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/heatingMagneticStirrerDriver"}, {"name": "Analysis_RobotdriversheatingMagneticStirrerDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversheatingMagneticStirrerDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver"}, {"name": "Analysis_RobotdriversmoistureAnalyzerDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/moistureAnalyzerDriver"}, {"name": "Analysis_RobotdriversmoistureAnalyzerDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversmoistureAnalyzerDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/moistureAnalyzerDriver"}, {"name": "Analysis_RobotdriversplcDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/plcDriver"}, {"name": "Analysis_RobotdriversplcDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversplcDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/plcDriver"}, {"name": "Analysis_RobotdriversrestInterfaceDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/restInterfaceDriver"}, {"name": "Analysis_RobotdriversrestInterfaceDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversrestInterfaceDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/restInterfaceDriver"}, {"name": "Analysis_RobotdriversrobotDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/robotDriver"}, {"name": "Analysis_RobotdriversrobotDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobotdriversrobotDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/drivers/robotDriver"}, {"name": "Analysis_Robottest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test"}, {"name": "Analysis_Robottest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_Robottest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test"}, {"name": "Analysis_RobottestaixsDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/aixsDriverTest"}, {"name": "Analysis_RobottestaixsDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestaixsDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/aixsDriverTest"}, {"name": "Analysis_RobottestbalanceDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/balanceDriverTest"}, {"name": "Analysis_RobottestbalanceDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestbalanceDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/balanceDriverTest"}, {"name": "Analysis_RobottestbalanceTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/balanceTest"}, {"name": "Analysis_RobottestbalanceTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestbalanceTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/balanceTest"}, {"name": "Analysis_RobottestheaterApiTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heaterApiTest"}, {"name": "Analysis_RobottestheaterApiTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestheaterApiTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/heaterApiTest"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heatingMagneticStirrerDriverTest"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/heatingMagneticStirrerDriverTest"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heatingMagneticStirrerDriver"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestheatingMagneticStirrerDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/heatingMagneticStirrerDriver"}, {"name": "Analysis_RobottestmoistureAnalyzerDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/moistureAnalyzerDriverTest"}, {"name": "Analysis_RobottestmoistureAnalyzerDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestmoistureAnalyzerDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/moistureAnalyzerDriverTest"}, {"name": "Analysis_Robottestnative_stirrer_test_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/native_stirrer_test"}, {"name": "Analysis_Robottestnative_stirrer_test_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_Robottestnative_stirrer_test_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/native_stirrer_test"}, {"name": "Analysis_RobottestpouringControlTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/pouringControlTest"}, {"name": "Analysis_RobottestpouringControlTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestpouringControlTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/pouringControlTest"}, {"name": "Analysis_RobottestrestInterfaceDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/restInterfaceDriverTest"}, {"name": "Analysis_RobottestrestInterfaceDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestrestInterfaceDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/restInterfaceDriverTest"}, {"name": "Analysis_RobottestrobotDriverTest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Analysis_Robot/test/robotDriverTest"}, {"name": "Analysis_RobottestrobotDriverTest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Analysis_RobottestrobotDriverTest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Analysis_Robot/test/robotDriverTest"}, {"name": "Boost_DATE_TIME_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_date_time-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Boost."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0"}, {"name": "Boost_FILESYSTEM_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"name": "Boost_IOSTREAMS_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_iostreams-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_SERIALIZATION_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_SYSTEM_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_system-vc142-mt-gd-x64-1_78.lib"}, {"name": "Boost_THREAD_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "C:/opt/PCL/3rdParty/Boost/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/newfuxios/cmake-build-debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "6"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/ctest.exe"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/MDd /Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/MD /O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/MD /O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/MD /Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /W3"}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "/MDd /Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/MD /O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "/MD /O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/MD /Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/machine:x64                                                                                                                                                                                                                                                                                                                                                                                                            "}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREADS_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthreads"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREAD_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthread"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/newfuxios"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files (x86)/Project"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "HELPSTRING", "value": "make program"}], "type": "FILEPATH", "value": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/ninja/win/x64/ninja.exe"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/machine:x64                                                                                                                                                                                                                                                                                                                                                                                                            "}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.19041.0/x64/mt.exe"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "84"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "Project"}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.19041.0/x64/rc.exe"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/machine:x64                                                                                                                                                                                                                                                                                                                                                                                                            "}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Boost", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake][cfound components: system filesystem date_time iostreams serialization thread program_options ][v1.78.0(1.65.0)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCV", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCV"}], "type": "INTERNAL", "value": "[C:/opt/opencv/build][v4.11.0()]"}, {"name": "FOUND", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/opt/robwork-21.12/lib/sdurw_math.lib"}, {"name": "MJServerAPP_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer/APP"}, {"name": "MJServerAPP_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServerAPP_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer/APP"}, {"name": "MJServer_RefactorApp_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/App"}, {"name": "MJServer_RefactorApp_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServer_RefactorApp_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer_Refactor/App"}, {"name": "MJServer_RefactorLibrary_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/Library"}, {"name": "MJServer_RefactorLibrary_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServer_RefactorLibrary_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer_Refactor/Library"}, {"name": "MJServer_RefactorTestphase1_test_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/phase1_test"}, {"name": "MJServer_RefactorTestphase1_test_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServer_RefactorTestphase1_test_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer_Refactor/Test/phase1_test"}, {"name": "MJServer_RefactorTestsimple_abb_client_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/simple_abb_client"}, {"name": "MJServer_RefactorTestsimple_abb_client_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServer_RefactorTestsimple_abb_client_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer_Refactor/Test/simple_abb_client"}, {"name": "MJServer_RefactorTestsimple_feeder_client_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/simple_feeder_client"}, {"name": "MJServer_RefactorTestsimple_feeder_client_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "MJServer_RefactorTestsimple_feeder_client_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/MJServer_Refactor/Test/simple_feeder_client"}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "C:/opt/opencv/build"}, {"name": "Project_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug"}, {"name": "Project_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "Project_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios"}, {"name": "Qt5Charts_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Charts."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts"}, {"name": "Qt5Core_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Core."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core"}, {"name": "Qt5Gui_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Gui."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui"}, {"name": "Qt5Network_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Network."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network"}, {"name": "Qt5SerialPort_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5SerialPort."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5SerialPort"}, {"name": "Qt5Sql_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Sql."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql"}, {"name": "Qt5Test_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Test."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Test"}, {"name": "Qt5Widgets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5Widgets."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets"}, {"name": "Qt5_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt5."}], "type": "PATH", "value": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5"}, {"name": "ROBWORK_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "ROBWORK_INCLUDE_DIRS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "ROBWORK_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "ROBWORK_LIBRARY_DIRS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "ROBWORK_VERSION", "properties": [{"name": "HELPSTRING", "value": "RobWork version"}], "type": "STRING", "value": "21.12.14"}, {"name": "RTTR_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for RTTR."}], "type": "PATH", "value": "C:/opt/rttr/cmake"}, {"name": "RW_CXX_FLAGS", "properties": [{"name": "HELPSTRING", "value": "Change this to force using your own\n                      flags and not those of RobWork"}], "type": "STRING", "value": "-<PERSON><PERSON><PERSON> -bigobj /MP -openmp"}, {"name": "RW_C_FLAGS", "properties": [{"name": "HELPSTRING", "value": "Change this to force using your own\n                      flags and not those of RobWork"}], "type": "STRING", "value": ""}, {"name": "RW_DEFINITIONS", "properties": [{"name": "HELPSTRING", "value": "Change this to force using your own\n                      definitions and not those of RobWork"}], "type": "STRING", "value": "-DNOMINMAX;-DBIND_FORTRAN_LOWERCASE_UNDERSCORE;-DWIN32_LEAN_AND_MEAN;-D_WIN32_WINNT=0x0501;-D_SCL_SECURE_NO_WARNINGS;-D_CRT_SECURE_NO_WARNINGS;-D_CRT_SECURE_NO_DEPRECATE;-DMSVC_AMD64"}, {"name": "RW_LINKER_FLAGS", "properties": [{"name": "HELPSTRING", "value": "Change this to force using your own linker\n                      flags and not those of RobWork"}], "type": "STRING", "value": ""}, {"name": "RobWork_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for RobWork."}], "type": "PATH", "value": "C:/opt/robwork-21.12/robwork-21.12/cmake"}, {"name": "RoboticLaserMarkingAbbDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/AbbDriver"}, {"name": "RoboticLaserMarkingAbbDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingAbbDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/AbbDriver"}, {"name": "RoboticLaserMarkingLicenseGenerator_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/LicenseGenerator"}, {"name": "RoboticLaserMarkingLicenseGenerator_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingLicenseGenerator_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/LicenseGenerator"}, {"name": "RoboticLaserMarkingRFIDDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/RFIDDriver"}, {"name": "RoboticLaserMarkingRFIDDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingRFIDDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/RFIDDriver"}, {"name": "RoboticLaserMarkingTestabbsocket_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/abbsocket"}, {"name": "RoboticLaserMarkingTestabbsocket_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingTestabbsocket_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/Test/abbsocket"}, {"name": "RoboticLaserMarkingTestlaserUI_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/laserUI"}, {"name": "RoboticLaserMarkingTestlaserUI_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingTestlaserUI_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/Test/laserUI"}, {"name": "RoboticLaserMarkingTestlaser_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/laser"}, {"name": "RoboticLaserMarkingTestlaser_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingTestlaser_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/Test/laser"}, {"name": "RoboticLaserMarkingTestrfiddriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/rfiddriver"}, {"name": "RoboticLaserMarkingTestrfiddriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingTestrfiddriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/Test/rfiddriver"}, {"name": "RoboticLaserMarkingTestrfidserver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/rfidserver"}, {"name": "RoboticLaserMarkingTestrfidserver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingTestrfidserver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/Test/rfidserver"}, {"name": "RoboticLaserMarkingUI_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/UI"}, {"name": "RoboticLaserMarkingUI_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkingUI_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/UI"}, {"name": "RoboticLaserMarkinglaserDriverSim_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/laserDriverSim"}, {"name": "RoboticLaserMarkinglaserDriverSim_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkinglaserDriverSim_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/laserDriverSim"}, {"name": "RoboticLaserMarkinglaserDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/laserDriver"}, {"name": "RoboticLaserMarkinglaserDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "RoboticLaserMarkinglaserDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/RoboticLaserMarking/laserDriver"}, {"name": "Testtest_abb_socket_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_abb_socket"}, {"name": "Testtest_abb_socket_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_abb_socket_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_abb_socket"}, {"name": "Testtest_config_manager_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_config_manager"}, {"name": "Testtest_config_manager_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_config_manager_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_config_manager"}, {"name": "Testtest_csv_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_csv"}, {"name": "Testtest_csv_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_csv_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_csv"}, {"name": "Testtest_event_listener_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_event_listener"}, {"name": "Testtest_event_listener_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_event_listener_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_event_listener"}, {"name": "Testtest_executor_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_executor"}, {"name": "Testtest_executor_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_executor_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_executor"}, {"name": "Testtest_executor_context_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_executor_context"}, {"name": "Testtest_executor_context_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_executor_context_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_executor_context"}, {"name": "Testtest_fa2204n_balance_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_fa2204n_balance"}, {"name": "Testtest_fa2204n_balance_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_fa2204n_balance_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_fa2204n_balance"}, {"name": "Testtest_fa2204n_balance_basic_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_fa2204n_balance_basic"}, {"name": "Testtest_fa2204n_balance_basic_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_fa2204n_balance_basic_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_fa2204n_balance_basic"}, {"name": "Testtest_fileutil_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_fileutil"}, {"name": "Testtest_fileutil_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_fileutil_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_fileutil"}, {"name": "Testtest_json_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_json"}, {"name": "Testtest_json_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_json_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_json"}, {"name": "Testtest_license_manager_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_license_manager"}, {"name": "Testtest_license_manager_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_license_manager_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_license_manager"}, {"name": "Testtest_license_ui_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_license_ui"}, {"name": "Testtest_license_ui_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_license_ui_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_license_ui"}, {"name": "Testtest_network_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_network"}, {"name": "Testtest_network_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_network_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_network"}, {"name": "Testtest_serial_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_serial"}, {"name": "Testtest_serial_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_serial_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_serial"}, {"name": "Testtest_service_container_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_service_container"}, {"name": "Testtest_service_container_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_service_container_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_service_container"}, {"name": "Testtest_socket_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_socket"}, {"name": "Testtest_socket_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_socket_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_socket"}, {"name": "Testtest_sqlite_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_sqlite"}, {"name": "Testtest_sqlite_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_sqlite_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_sqlite"}, {"name": "Testtest_taskflow_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_taskflow"}, {"name": "Testtest_taskflow_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_taskflow_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_taskflow"}, {"name": "Testtest_twoaixsrobot_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot"}, {"name": "Testtest_twoaixsrobot_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_twoaixsrobot_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_twoaixsrobot"}, {"name": "Testtest_xml_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_xml"}, {"name": "Testtest_xml_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Testtest_xml_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_xml"}, {"name": "boost_atomic_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_atomic."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0"}, {"name": "boost_chrono_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_chrono."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0"}, {"name": "boost_date_time_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_date_time."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0"}, {"name": "boost_filesystem_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_filesystem."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0"}, {"name": "boost_headers_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_headers."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0"}, {"name": "boost_iostreams_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_iostreams."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0"}, {"name": "boost_program_options_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_program_options."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0"}, {"name": "boost_serialization_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_serialization."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0"}, {"name": "boost_system_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_system."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0"}, {"name": "boost_thread_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_thread."}], "type": "PATH", "value": "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0"}, {"name": "fuxicommon_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/fuxicommon"}, {"name": "fuxicommon_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "fuxicommon_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/fuxicommon"}, {"name": "fuxicore_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/fuxicore"}, {"name": "fuxicore_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "fuxicore_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/fuxicore"}, {"name": "hardwaredriverAuboArcsDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/AuboArcsDriver"}, {"name": "hardwaredriverAuboArcsDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverAuboArcsDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/AuboArcsDriver"}, {"name": "hardwaredriverAuboDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/AuboDriver"}, {"name": "hardwaredriverAuboDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverAuboDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/AuboDriver"}, {"name": "hardwaredriverElectricGripperDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/ElectricGripperDriver"}, {"name": "hardwaredriverElectricGripperDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverElectricGripperDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/ElectricGripperDriver"}, {"name": "hardwaredriverHikVisionCamera_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/HikVisionCamera"}, {"name": "hardwaredriverHikVisionCamera_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverHikVisionCamera_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/HikVisionCamera"}, {"name": "hardwaredriverLabelPrinter_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/LabelPrinter"}, {"name": "hardwaredriverLabelPrinter_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverLabelPrinter_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/LabelPrinter"}, {"name": "hardwaredriverMettlerBalance_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/MettlerBalance"}, {"name": "hardwaredriverMettlerBalance_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverMettlerBalance_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/MettlerBalance"}, {"name": "hardwaredriverOpcDa_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/OpcDa"}, {"name": "hardwaredriverOpcDa_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverOpcDa_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/OpcDa"}, {"name": "hardwaredriverOpcUa_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/OpcUa"}, {"name": "hardwaredriverOpcUa_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverOpcUa_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/OpcUa"}, {"name": "hardwaredriverabbRobotDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/abbRobotDriver"}, {"name": "hardwaredriverabbRobotDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverabbRobotDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/abbRobotDriver"}, {"name": "hardwaredriveragilerobotDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/agilerobotDriver"}, {"name": "hardwaredriveragilerobotDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriveragilerobotDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/agilerobotDriver"}, {"name": "hardwaredriverfairinoDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/fairinoDriver"}, {"name": "hardwaredriverfairinoDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverfairinoDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/fairinoDriver"}, {"name": "hardwaredriverjunduoHandDriver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/junduoHandDriver"}, {"name": "hardwaredriverjunduoHandDriver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverjunduoHandDriver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/junduoHandDriver"}, {"name": "hardwaredrivermodbus_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/modbus"}, {"name": "hardwaredrivermodbus_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredrivermodbus_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/modbus"}, {"name": "hardwaredriverserial_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/serial"}, {"name": "hardwaredriverserial_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverserial_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/serial"}, {"name": "hardwaredriversocket_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/socket"}, {"name": "hardwaredriversocket_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriversocket_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/socket"}, {"name": "hardwaredriverusbcamera_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/hardwaredriver/usbcamera"}, {"name": "hardwaredriverusbcamera_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "hardwaredriverusbcamera_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/hardwaredriver/usbcamera"}, {"name": "test_micro_dosing_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/Test/test_micro_dosing"}, {"name": "test_micro_dosing_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "test_micro_dosing_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/Test/test_micro_dosing"}, {"name": "tmp", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib"}, {"name": "toolcalbuild_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/calbuild"}, {"name": "toolcalbuild_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolcalbuild_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/calbuild"}, {"name": "toolcaltest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/caltest"}, {"name": "toolcaltest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolcaltest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/caltest"}, {"name": "toolcameraCalibrator_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/cameraCalibrator"}, {"name": "toolcameraCalibrator_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolcameraCalibrator_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/cameraCalibrator"}, {"name": "toolcommunication_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/communication"}, {"name": "toolcommunication_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolcommunication_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/communication"}, {"name": "toolhandeyecal_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/handeyecal"}, {"name": "toolhandeyecal_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolhandeyecal_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/handeyecal"}, {"name": "toolhandeyecaltest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/handeyecaltest"}, {"name": "toolhandeyecaltest_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolhandeyecaltest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/handeyecaltest"}, {"name": "toolhandeyecaluihandeyecalui_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/handeyecalui/handeyecalui"}, {"name": "toolhandeyecaluihandeyecalui_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolhandeyecaluihandeyecalui_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/handeyecalui/handeyecalui"}, {"name": "toolhandeyecaluipathAuto_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/handeyecaluipathAuto"}, {"name": "toolhandeyecaluipathAuto_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolhandeyecaluipathAuto_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/handeyecaluipathAuto"}, {"name": "toolhandeyecaluipath_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/handeyecaluipath"}, {"name": "toolhandeyecaluipath_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolhandeyecaluipath_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/handeyecaluipath"}, {"name": "toolverify_calibration_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/cmake-build-debug/tool/verify_calibration"}, {"name": "toolverify_calibration_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "toolverify_calibration_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/newfuxios/tool/verify_calibration"}], "kind": "cache", "version": {"major": 2, "minor": 0}}