{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["MJServer_Refactor/Test/simple_feeder_client/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"backtrace": 0, "id": "MJServer_RefactorLibrary::@8670365571700e12b583"}, {"backtrace": 0, "id": "fuxicommon::@58335e9a86196d0a97e7"}], "id": "MJServer_RefactorTestsimple_feeder_client_autogen::@80cf03468317b5d7fe2b", "isGeneratorProvided": true, "name": "MJServer_RefactorTestsimple_feeder_client_autogen", "paths": {"build": "MJServer_Refactor/Test/simple_feeder_client", "source": "MJServer_Refactor/Test/simple_feeder_client"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/MJServer_Refactor/Test/simple_feeder_client/CMakeFiles/MJServer_RefactorTestsimple_feeder_client_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/MJServer_Refactor/Test/simple_feeder_client/CMakeFiles/MJServer_RefactorTestsimple_feeder_client_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}