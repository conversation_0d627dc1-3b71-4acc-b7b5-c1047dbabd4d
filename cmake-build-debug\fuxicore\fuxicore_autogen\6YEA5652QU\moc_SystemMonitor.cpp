/****************************************************************************
** Meta object code from reading C++ file 'SystemMonitor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../fuxicore/include/SystemMonitor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SystemMonitor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_Fuxi__Core__SystemMonitor_t {
    QByteArrayData data[22];
    char stringdata0[285];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Fuxi__Core__SystemMonitor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Fuxi__Core__SystemMonitor_t qt_meta_stringdata_Fuxi__Core__SystemMonitor = {
    {
QT_MOC_LITERAL(0, 0, 25), // "Fuxi::Core::SystemMonitor"
QT_MOC_LITERAL(1, 26, 14), // "metricsUpdated"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 13), // "SystemMetrics"
QT_MOC_LITERAL(4, 56, 7), // "metrics"
QT_MOC_LITERAL(5, 64, 19), // "systemStatusChanged"
QT_MOC_LITERAL(6, 84, 12), // "SystemStatus"
QT_MOC_LITERAL(7, 97, 9), // "oldStatus"
QT_MOC_LITERAL(8, 107, 9), // "newStatus"
QT_MOC_LITERAL(9, 117, 14), // "alertTriggered"
QT_MOC_LITERAL(10, 132, 11), // "SystemAlert"
QT_MOC_LITERAL(11, 144, 5), // "alert"
QT_MOC_LITERAL(12, 150, 17), // "healthCheckFailed"
QT_MOC_LITERAL(13, 168, 7), // "checkId"
QT_MOC_LITERAL(14, 176, 5), // "error"
QT_MOC_LITERAL(15, 182, 17), // "customMetricAdded"
QT_MOC_LITERAL(16, 200, 4), // "name"
QT_MOC_LITERAL(17, 205, 5), // "value"
QT_MOC_LITERAL(18, 211, 14), // "collectMetrics"
QT_MOC_LITERAL(19, 226, 24), // "runScheduledHealthChecks"
QT_MOC_LITERAL(20, 251, 18), // "evaluateAlertRules"
QT_MOC_LITERAL(21, 270, 14) // "cleanupOldData"

    },
    "Fuxi::Core::SystemMonitor\0metricsUpdated\0"
    "\0SystemMetrics\0metrics\0systemStatusChanged\0"
    "SystemStatus\0oldStatus\0newStatus\0"
    "alertTriggered\0SystemAlert\0alert\0"
    "healthCheckFailed\0checkId\0error\0"
    "customMetricAdded\0name\0value\0"
    "collectMetrics\0runScheduledHealthChecks\0"
    "evaluateAlertRules\0cleanupOldData"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Fuxi__Core__SystemMonitor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   59,    2, 0x06 /* Public */,
       5,    2,   62,    2, 0x06 /* Public */,
       9,    1,   67,    2, 0x06 /* Public */,
      12,    2,   70,    2, 0x06 /* Public */,
      15,    2,   75,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      18,    0,   80,    2, 0x08 /* Private */,
      19,    0,   81,    2, 0x08 /* Private */,
      20,    0,   82,    2, 0x08 /* Private */,
      21,    0,   83,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6, 0x80000000 | 6,    7,    8,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   13,   14,
    QMetaType::Void, QMetaType::QString, QMetaType::Double,   16,   17,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void Fuxi::Core::SystemMonitor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SystemMonitor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->metricsUpdated((*reinterpret_cast< const SystemMetrics(*)>(_a[1]))); break;
        case 1: _t->systemStatusChanged((*reinterpret_cast< SystemStatus(*)>(_a[1])),(*reinterpret_cast< SystemStatus(*)>(_a[2]))); break;
        case 2: _t->alertTriggered((*reinterpret_cast< const SystemAlert(*)>(_a[1]))); break;
        case 3: _t->healthCheckFailed((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->customMetricAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 5: _t->collectMetrics(); break;
        case 6: _t->runScheduledHealthChecks(); break;
        case 7: _t->evaluateAlertRules(); break;
        case 8: _t->cleanupOldData(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SystemMonitor::*)(const SystemMetrics & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SystemMonitor::metricsUpdated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SystemMonitor::*)(SystemStatus , SystemStatus );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SystemMonitor::systemStatusChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SystemMonitor::*)(const SystemAlert & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SystemMonitor::alertTriggered)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SystemMonitor::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SystemMonitor::healthCheckFailed)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SystemMonitor::*)(const QString & , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SystemMonitor::customMetricAdded)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject Fuxi::Core::SystemMonitor::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_Fuxi__Core__SystemMonitor.data,
    qt_meta_data_Fuxi__Core__SystemMonitor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *Fuxi::Core::SystemMonitor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Fuxi::Core::SystemMonitor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Fuxi__Core__SystemMonitor.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Fuxi::Core::SystemMonitor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void Fuxi::Core::SystemMonitor::metricsUpdated(const SystemMetrics & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void Fuxi::Core::SystemMonitor::systemStatusChanged(SystemStatus _t1, SystemStatus _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void Fuxi::Core::SystemMonitor::alertTriggered(const SystemAlert & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void Fuxi::Core::SystemMonitor::healthCheckFailed(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void Fuxi::Core::SystemMonitor::customMetricAdded(const QString & _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
